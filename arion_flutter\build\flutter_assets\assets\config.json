{"api": {"baseUrl": "https://dxncozbhwppvwpugoqjk.supabase.co/functions/v1", "endpoint": "/quick-processor", "actions": {"initSession": "init-session", "queryAgent": "query-agent", "queryRag": "query-rag", "generateDocuments": "generate-documents", "getDocuments": "get-documents", "getDocument": "get-document", "saveContact": "save-contact", "logInteraction": "log-interaction", "logDocumentView": "log-document-view", "logEvent": "log-event", "requestDemoAccess": "request-demo-access", "verifyEmail": "verify-email", "resendVerification": "resend-verification", "submitPilotApplication": "submit-pilot-application", "adminRequestCode": "admin-request-code", "adminVerifyCode": "admin-verify-code", "verifyAdminSession": "verify-admin-session", "getLeads": "get-leads", "authenticateUser": "authenticate-user"}}, "supabase": {"anonKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4bmNvemJod3BwdndwdWdvcWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxOTgxMDQsImV4cCI6MjA2ODc3NDEwNH0.zx_wwG5nnNyxUwpUqJldQfNzSSfxgud4C4x0Bvx-r90"}}