import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/auth/auth_cubit.dart';
import '../routes/app_router.dart';
import 'admin_mfa_dialog.dart';

class LoginDialog extends StatefulWidget {
  const LoginDialog({super.key});

  @override
  State<LoginDialog> createState() => _LoginDialogState();
}

class _LoginDialogState extends State<LoginDialog> {
  final _emailCtrl = TextEditingController();
  final _pwdCtrl = TextEditingController();
  String? _emailError;
  String? _pwdError;
  bool _submitting = false;
  String? _warning;

  @override
  void dispose() {
    _emailCtrl.dispose();
    _pwdCtrl.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    setState(() { _emailError = null; _pwdError = null; _warning = null; });
    final email = _emailCtrl.text.trim();
    final pwd = _pwdCtrl.text.trim();
    if (email.isEmpty) { setState(() => _emailError = 'Please enter your email address'); return; }
    if (!RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$').hasMatch(email)) { setState(() => _emailError = 'Please enter a valid email address'); return; }
    if (pwd.isEmpty) { setState(() => _pwdError = 'Please enter your password'); return; }

    setState(() => _submitting = true);
    final cubit = context.read<AuthCubit>();
    await cubit.signIn(email, pwd);
    final state = cubit.state;
    if (!mounted) return;
    if (state.status == AuthStatus.authenticated) {
      if (state.isAdmin) {
        final data = state.data ?? {};
        await Future.any([
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) => _AdminSuccess(
              userEmail: (data['userEmail'] ?? email) as String,
              role: (data['adminRole'] ?? 'admin') as String,
              permissions: List<String>.from(data['permissions'] ?? const []),
            ),
          ),
          Future.delayed(const Duration(seconds: 2), () {
            if (mounted) {
              Navigator.of(context, rootNavigator: true).pop();
            }
          })
        ]);
        // Optional: Prompt for admin MFA if enabled
        final token = await showDialog(context: context, builder: (_) => const AdminMfaDialog());
        if (token is String && token.isNotEmpty) {
          // store token if needed
        }
      }
      Navigator.of(context).pop();
      Navigator.of(context).pushNamed(AppRouter.demoRoute);
    } else if (state.status == AuthStatus.failure) {
      final isAdmin = email.toLowerCase().contains('@arionetworks.com');
      setState(() => isAdmin ? _pwdError = 'Invalid admin credentials' : _warning = 'Email not found. Please register.');
    }
    if (mounted) setState(() => _submitting = false);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text('Access Demo', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
              const SizedBox(height: 6),
              const Text('Enter your registered email to continue'),
              const SizedBox(height: 16),
              TextField(
                controller: _emailCtrl,
                decoration: InputDecoration(labelText: 'Email Address *', errorText: _emailError),
                onSubmitted: (_) => FocusScope.of(context).nextFocus(),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _pwdCtrl,
                decoration: InputDecoration(labelText: 'Password *', errorText: _pwdError),
                obscureText: true,
                onSubmitted: (_) => _submit(),
              ),
              if (_warning != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(color: const Color(0xFFFef3c7), border: Border.all(color: const Color(0xFFF59e0b)), borderRadius: BorderRadius.circular(8)),
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    const Text('Email not found', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF92400E))),
                    const SizedBox(height: 4),
                    Text(_warning!, style: const TextStyle(color: Color(0xFF92400E))),
                  ]),
                )
              ],
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(onPressed: _submitting ? null : () => Navigator.of(context).pop(), child: const Text('Cancel')),
                  const SizedBox(width: 8),
                  ElevatedButton(onPressed: _submitting ? null : _submit, child: Text(_submitting ? 'Signing in...' : 'Continue to Demo')),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

class _AdminSuccess extends StatelessWidget {
  final String userEmail;
  final String role;
  final List<String> permissions;
  const _AdminSuccess({required this.userEmail, required this.role, required this.permissions});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Admin Access Granted!', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Color(0xFF059669))),
            const SizedBox(height: 8),
            Text('Welcome back, ${userEmail.split('@').first}'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(color: Color(0xFFECFDF5), borderRadius: BorderRadius.all(Radius.circular(8))),
              child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Text('Role: $role'),
                Text('Permissions: ${permissions.join(', ')}'),
              ]),
            ),
            const SizedBox(height: 8),
            const Text('Redirecting to demo platform with admin privileges...'),
            const SizedBox(height: 8),
            const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}


