import 'package:flutter/material.dart';
import '../views/landing_page.dart';
import '../views/demo_page.dart';
import '../views/admin_page.dart';
import '../views/verify_page.dart';

class AppRouter {
  static const String initialRoute = '/';
  static const String demoRoute = '/demo';
  static const String adminRoute = '/admin';
  static const String verifyRoute = '/verify';

  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case initialRoute:
        return MaterialPageRoute(builder: (_) => const LandingPage());
      case demoRoute:
        return MaterialPageRoute(builder: (_) => const DemoPage());
      case adminRoute:
        return MaterialPageRoute(builder: (_) => const AdminPage());
      case verifyRoute:
        String? token = (settings.arguments as Map?)?['token'] as String?;
        final name = settings.name;
        if ((token == null || token.isEmpty) && name != null) {
          final uri = Uri.parse(name);
          token = uri.queryParameters['token'];
        }
        return MaterialPageRoute(builder: (_) => VerifyPage(token: token));
      default:
        return MaterialPageRoute(builder: (_) => const LandingPage());
    }
  }
}


