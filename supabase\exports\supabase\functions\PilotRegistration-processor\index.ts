import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
// ===== CONSTANTS =====
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-api-key, x-session-id, accept',
  'Access-Control-Allow-Methods': 'POST, OPTIONS, GET',
  'Access-Control-Max-Age': '86400'
};
const RESPONSE_HEADERS = {
  'Content-Type': 'application/json',
  ...CORS_HEADERS
};
const RATE_LIMIT_KEY_PREFIX = 'product_registration_rate_limit';
const DEFAULT_RATE_LIMIT = 8; // requests per hour
const RECAPTCHA_MIN_SCORE = 0.5;
// Valid product IDs (whitelist approach for security)
const VALID_PRODUCT_IDS = [
  'arioncomply',
  'arionsecure',
  'arionanalytics',
  'arionplatform',
  'default'
];
const VALID_PROGRAM_TYPES = [
  'pilot',
  'waitlist',
  'beta',
  'early_access'
];
const VALID_INDUSTRIES = [
  'Technology',
  'Financial Services',
  'Healthcare',
  'Manufacturing',
  'Government',
  'Education',
  'Retail',
  'Energy',
  'Telecommunications',
  'Other'
];
const VALID_COMPANY_SIZES = [
  '1-10',
  '11-50',
  '51-200',
  '201-1000',
  '1001-5000',
  '5000+'
];
const VALID_TIMELINES = [
  'Immediately',
  '1-3 months',
  '3-6 months',
  '6-12 months',
  '12+ months',
  'Just exploring'
];
// ===== ENVIRONMENT VARIABLES =====
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');
const recaptchaSecret = Deno.env.get('RECAPTCHA_SECRET_KEY');
const rateLimitPerHour = parseInt(Deno.env.get('RATE_LIMIT_REQUESTS_PER_HOUR') || DEFAULT_RATE_LIMIT.toString());
const redirectUrl = Deno.env.get('REDIRECT_URL') || 'http://*************:8002/registration-success.html';
// Validate required environment variables
if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
  throw new Error('Missing required environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, SUPABASE_ANON_KEY');
}
// Create Supabase clients
const supabaseService = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey);
// ===== UTILITY FUNCTIONS =====
function getClientIP(request) {
  return request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() || request.headers.get('x-real-ip') || request.headers.get('cf-connecting-ip') || '127.0.0.1';
}
function getUserAgent(request) {
  return request.headers.get('user-agent') || 'Unknown';
}
function sanitizeInput(input) {
  return input.trim().replace(/[<>]/g, '');
}
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
}
function validatePhone(phone) {
  const phoneRegex = /^[\d\s\-\+\(\)]{10,20}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}
function validateRequired(value, minLength = 1) {
  return value && value.trim().length >= minLength;
}
function validateUrl(url) {
  try {
    new URL(url);
    return true;
  } catch  {
    return false;
  }
}
function getProductName(productId) {
  const productNames = {
    'arioncomply': 'ArionComply',
    'arionsecure': 'ArionSecure',
    'arionanalytics': 'ArionAnalytics',
    'arionplatform': 'ArionPlatform',
    'default': 'ArionNetworks'
  };
  return productNames[productId] || 'ArionNetworks';
}
// ===== VALIDATION FUNCTIONS =====
function validateRegistrationData(data) {
  const errors = [];
  // Required field validation
  const requiredFields = {
    product_id: 'Product ID',
    program_type: 'Program Type',
    full_name: 'Full Name',
    email: 'Email Address',
    company: 'Company',
    job_title: 'Job Title',
    primary_business: 'Primary Business',
    company_size: 'Company Size',
    phone: 'Phone Number',
    use_case: 'Use Case'
  };
  Object.entries(requiredFields).forEach(([field, label])=>{
    if (!validateRequired(data[field])) {
      errors.push({
        field,
        message: `${label} is required`
      });
    }
  });
  // Specific field validations
  if (data.full_name && (data.full_name.length < 2 || data.full_name.length > 100)) {
    errors.push({
      field: 'full_name',
      message: 'Full name must be between 2 and 100 characters'
    });
  }
  if (data.email && !validateEmail(data.email)) {
    errors.push({
      field: 'email',
      message: 'Please enter a valid email address'
    });
  }
  if (data.company && (data.company.length < 2 || data.company.length > 100)) {
    errors.push({
      field: 'company',
      message: 'Company name must be between 2 and 100 characters'
    });
  }
  if (data.job_title && (data.job_title.length < 2 || data.job_title.length > 100)) {
    errors.push({
      field: 'job_title',
      message: 'Job title must be between 2 and 100 characters'
    });
  }
  if (data.phone && !validatePhone(data.phone)) {
    errors.push({
      field: 'phone',
      message: 'Please enter a valid phone number (10-20 digits)'
    });
  }
  if (data.use_case && (data.use_case.length < 20 || data.use_case.length > 1000)) {
    errors.push({
      field: 'use_case',
      message: 'Use case description must be between 20 and 1000 characters'
    });
  }
  // Whitelist validations for security
  if (data.product_id && !VALID_PRODUCT_IDS.includes(data.product_id)) {
    errors.push({
      field: 'product_id',
      message: 'Invalid product ID'
    });
  }
  if (data.program_type && !VALID_PROGRAM_TYPES.includes(data.program_type)) {
    errors.push({
      field: 'program_type',
      message: 'Invalid program type'
    });
  }
  if (data.primary_business && !VALID_INDUSTRIES.includes(data.primary_business)) {
    errors.push({
      field: 'primary_business',
      message: 'Please select a valid industry'
    });
  }
  if (data.company_size && !VALID_COMPANY_SIZES.includes(data.company_size)) {
    errors.push({
      field: 'company_size',
      message: 'Please select a valid company size'
    });
  }
  if (data.timeline && data.timeline !== '' && !VALID_TIMELINES.includes(data.timeline)) {
    errors.push({
      field: 'timeline',
      message: 'Please select a valid timeline'
    });
  }
  // URL validation for tracking fields
  if (data.source_url && data.source_url !== '' && !validateUrl(data.source_url)) {
    errors.push({
      field: 'source_url',
      message: 'Invalid source URL format'
    });
  }
  return errors;
}
// ===== SECURITY FUNCTIONS =====
async function verifyRecaptcha(token, clientIP) {
  if (!recaptchaSecret) {
    console.warn('reCAPTCHA not configured - allowing request in development mode');
    return {
      success: true
    };
  }
  if (recaptchaSecret === 'YOUR_RECAPTCHA_SECRET_KEY') {
    console.warn('reCAPTCHA using placeholder key - allowing request in development mode');
    return {
      success: true
    };
  }
  try {
    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        secret: recaptchaSecret,
        response: token,
        remoteip: clientIP
      })
    });
    if (!response.ok) {
      throw new Error(`reCAPTCHA API error: ${response.status}`);
    }
    const result = await response.json();
    if (!result.success) {
      console.log(`reCAPTCHA verification failed for IP ${clientIP}:`, result['error-codes']);
      return {
        success: false,
        error: 'reCAPTCHA verification failed'
      };
    }
    // Check score for reCAPTCHA v3
    if (result.score !== undefined && result.score < RECAPTCHA_MIN_SCORE) {
      console.log(`Low reCAPTCHA score (${result.score}) for IP ${clientIP}`);
      return {
        success: false,
        error: 'Security score too low',
        score: result.score
      };
    }
    return {
      success: true,
      score: result.score
    };
  } catch (error) {
    console.error('reCAPTCHA verification error:', error);
    return {
      success: false,
      error: 'reCAPTCHA verification failed'
    };
  }
}
async function checkRateLimit(clientIP, productId) {
  // Enhanced rate limiting per IP per product
  // In production, consider using Supabase Edge Function KV store or Redis
  const rateLimitKey = `${RATE_LIMIT_KEY_PREFIX}:${clientIP}:${productId}`;
  try {
    // For now, we'll implement a basic rate limit check
    // This is a placeholder - in production you'd want to use persistent storage
    return {
      allowed: true,
      count: 0
    };
  } catch (error) {
    console.error('Rate limit check error:', error);
    return {
      allowed: true,
      count: 0
    }; // Allow on error to avoid blocking legitimate users
  }
}
// ===== DATABASE FUNCTIONS =====
async function checkDuplicateVerifiedRegistration(email, productId, programType) {
  try {
    const { data, error } = await supabaseService.from('product_registrations').select('id').eq('email', email.toLowerCase()).eq('product_id', productId).eq('program_type', programType).eq('email_verified', true).single();
    if (error && error.code !== 'PGRST116') {
      console.error('Database error checking duplicate:', error);
      return false; // Allow registration on database error
    }
    return !!data;
  } catch (error) {
    console.error('Error checking duplicate registration:', error);
    return false;
  }
}
async function createAuthUserAndRegistration(data, clientIP, userAgent) {
  try {
    // First, create registration record without user_id
    const sanitizedData = {
      // No user_id initially - we'll link it after auth user is created
      product_id: sanitizeInput(data.product_id),
      program_type: sanitizeInput(data.program_type),
      source_url: data.source_url ? sanitizeInput(data.source_url) : null,
      utm_source: data.utm_source ? sanitizeInput(data.utm_source) : null,
      utm_campaign: data.utm_campaign ? sanitizeInput(data.utm_campaign) : null,
      // Personal information
      full_name: sanitizeInput(data.full_name),
      email: sanitizeInput(data.email.toLowerCase()),
      company: sanitizeInput(data.company),
      job_title: sanitizeInput(data.job_title),
      primary_business: sanitizeInput(data.primary_business),
      company_size: sanitizeInput(data.company_size),
      phone: sanitizeInput(data.phone),
      use_case: sanitizeInput(data.use_case),
      timeline: data.timeline ? sanitizeInput(data.timeline) : null,
      // Technical metadata
      ip_address: clientIP,
      user_agent: userAgent,
      submission_source: 'web',
      // Status
      status: 'pending_verification',
      email_verified: false
    };
    // Create registration first
    const { data: insertedData, error: insertError } = await supabaseService.from('product_registrations').insert([
      sanitizedData
    ]).select('id, email, product_id, program_type, full_name, company').single();
    if (insertError) {
      console.error('Database insertion error:', insertError);
      return {
        success: false,
        error: 'Registration data could not be saved. Please try again.'
      };
    }
    // Now create Supabase Auth user
    const { data: authData, error: authError } = await supabaseAuth.auth.signUp({
      email: data.email.toLowerCase(),
      password: generateTemporaryPassword(),
      options: {
        emailRedirectTo: `${redirectUrl}?product=${data.product_id}&program=${data.program_type}`,
        data: {
          full_name: data.full_name,
          product_id: data.product_id,
          program_type: data.program_type,
          registration_id: insertedData.id
        }
      }
    });
    if (authError) {
      console.error('Auth user creation error:', authError);
      // Clean up the registration if auth user creation fails
      await supabaseService.from('product_registrations').delete().eq('id', insertedData.id);
      if (authError.message.includes('already registered')) {
        return {
          success: false,
          error: 'This email is already registered. Please check your email for a verification link or try signing in.'
        };
      }
      return {
        success: false,
        error: 'Failed to create account. Please try again.'
      };
    }
    if (!authData.user) {
      // Clean up registration
      await supabaseService.from('product_registrations').delete().eq('id', insertedData.id);
      return {
        success: false,
        error: 'Failed to create user account'
      };
    }
    // Link the registration to the auth user (optional - skip if problematic)
    try {
      const { error: linkError } = await supabaseService.from('product_registrations').update({
        user_id: authData.user.id
      }).eq('id', insertedData.id);
      if (linkError) {
        console.error('Error linking registration to user (non-critical):', linkError);
      // Don't fail the process - the registration works without this link
      // The database trigger will handle linking when email is verified
      }
    } catch (linkingError) {
      console.error('Failed to link user to registration:', linkingError);
    // Continue without linking - not critical for the registration process
    }
    return {
      success: true,
      user: authData.user,
      registration: insertedData
    };
  } catch (error) {
    console.error('Error in createAuthUserAndRegistration:', error);
    return {
      success: false,
      error: 'An unexpected error occurred during registration'
    };
  }
}
function generateTemporaryPassword() {
  // Generate a secure temporary password
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for(let i = 0; i < 16; i++){
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}
// ===== NOTIFICATION FUNCTIONS =====
async function sendNotificationToTeam(data, registrationId, userId) {
  try {
    console.log(`New ${data.product_id} ${data.program_type} registration (pending email verification):`, {
      registration_id: registrationId,
      user_id: userId,
      product: data.product_id,
      program: data.program_type,
      company: data.company,
      email: data.email,
      status: 'pending_verification'
    });
  // In production, you might want to:
  // 1. Send Slack notification
  // 2. Send email to product team
  // 3. Create ticket in project management system
  // 4. Update CRM system
  } catch (error) {
    console.error('Failed to send team notification:', error);
  // Don't fail the registration if notification fails
  }
}
// ===== MAIN HANDLER =====
serve(async (request)=>{
  // Handle CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response('ok', {
      headers: CORS_HEADERS
    });
  }
  // Only allow POST requests for registration
  if (request.method !== 'POST') {
    return new Response(JSON.stringify({
      error: 'Method not allowed'
    }), {
      status: 405,
      headers: RESPONSE_HEADERS
    });
  }
  const startTime = Date.now();
  const clientIP = getClientIP(request);
  const userAgent = getUserAgent(request);
  console.log(`Product registration attempt from IP: ${clientIP}, User-Agent: ${userAgent}`);
  try {
    // Parse request body
    let requestData;
    try {
      requestData = await request.json();
    } catch (error) {
      console.error('JSON parsing error:', error);
      return new Response(JSON.stringify({
        error: 'Invalid JSON in request body'
      }), {
        status: 400,
        headers: RESPONSE_HEADERS
      });
    }
    // 1. Honeypot validation
    if (requestData.hp_field && requestData.hp_field.trim() !== '') {
      console.log(`Honeypot triggered from IP: ${clientIP}, value: "${requestData.hp_field}"`);
      return new Response(JSON.stringify({
        error: 'Invalid submission detected'
      }), {
        status: 400,
        headers: RESPONSE_HEADERS
      });
    }
    // 2. Rate limiting check (per IP per product)
    const rateLimitResult = await checkRateLimit(clientIP, requestData.product_id || 'unknown');
    if (!rateLimitResult.allowed) {
      console.log(`Rate limit exceeded for IP: ${clientIP}, product: ${requestData.product_id}, count: ${rateLimitResult.count}`);
      return new Response(JSON.stringify({
        error: 'Too many requests for this product. Please try again later.'
      }), {
        status: 429,
        headers: RESPONSE_HEADERS
      });
    }
    // 3. Input validation
    const validationErrors = validateRegistrationData(requestData);
    if (validationErrors.length > 0) {
      console.log(`Validation failed for IP: ${clientIP}`, validationErrors);
      return new Response(JSON.stringify({
        error: 'Validation failed',
        errors: validationErrors
      }), {
        status: 400,
        headers: RESPONSE_HEADERS
      });
    }
    // 4. reCAPTCHA verification
    const recaptchaResult = await verifyRecaptcha(requestData.recaptcha_token, clientIP);
    if (!recaptchaResult.success) {
      console.log(`reCAPTCHA failed for IP: ${clientIP}, product: ${requestData.product_id}, error: ${recaptchaResult.error}`);
      return new Response(JSON.stringify({
        error: 'Security validation failed. Please try again.'
      }), {
        status: 400,
        headers: RESPONSE_HEADERS
      });
    }
    // 5. Check for duplicate VERIFIED registration
    const isDuplicate = await checkDuplicateVerifiedRegistration(requestData.email, requestData.product_id, requestData.program_type);
    if (isDuplicate) {
      console.log(`Duplicate verified registration attempt for email: ${requestData.email}, product: ${requestData.product_id}, program: ${requestData.program_type}`);
      return new Response(JSON.stringify({
        error: `This email is already registered and verified for the ${requestData.product_id} ${requestData.program_type} program`
      }), {
        status: 409,
        headers: RESPONSE_HEADERS
      });
    }
    // 6. Create Auth user and registration
    const createResult = await createAuthUserAndRegistration(requestData, clientIP, userAgent);
    if (!createResult.success) {
      return new Response(JSON.stringify({
        error: createResult.error || 'Registration failed'
      }), {
        status: 500,
        headers: RESPONSE_HEADERS
      });
    }
    // 7. Send notification to team (async, don't block response)
    if (createResult.registration.id && createResult.user.id) {
      sendNotificationToTeam(requestData, createResult.registration.id, createResult.user.id).catch((error)=>{
        console.error('Team notification failed:', error);
      });
    }
    // 8. Success response
    const processingTime = Date.now() - startTime;
    console.log(`Registration successful for ${requestData.email} in ${requestData.product_id} ${requestData.program_type}, processing time: ${processingTime}ms`);
    // Generate personalized success message
    const productName = getProductName(requestData.product_id);
    const programName = {
      'pilot': 'Pilot Program',
      'waitlist': 'Waitlist',
      'beta': 'Beta Program',
      'early_access': 'Early Access Program'
    }[requestData.program_type] || 'Program';
    const response = {
      success: true,
      message: `Registration successful! Please check your email (${requestData.email}) and click the verification link to complete your ${productName} ${programName} registration.`,
      registration_id: createResult.registration.id,
      user_id: createResult.user.id,
      email_sent: true,
      next_step: 'email_verification'
    };
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: RESPONSE_HEADERS
    });
  } catch (error) {
    console.error('Unexpected error in product registration handler:', error);
    return new Response(JSON.stringify({
      error: 'An unexpected error occurred. Please try again later.'
    }), {
      status: 500,
      headers: RESPONSE_HEADERS
    });
  }
});
export { serve };
