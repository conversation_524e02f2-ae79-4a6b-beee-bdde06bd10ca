import 'package:get_it/get_it.dart';
import '../network/api_client.dart';
import '../services/session_service.dart';
import '../services/config_service.dart';
import '../../data/repositories/auth_repository.dart';
import '../../data/repositories/demo_access_repository.dart';
import '../../data/repositories/chat_repository.dart';
import '../../data/repositories/document_repository.dart';
import '../services/compliance_filter.dart';
import '../../data/repositories/admin_repository.dart';

final GetIt locator = GetIt.instance;

void setupLocator() {
  if (locator.isRegistered<ApiClient>()) return;

  locator.registerLazySingleton<ConfigService>(() => ConfigService());
  locator.registerLazySingleton<ApiClient>(() => ApiClient(locator()));
  locator.registerLazySingleton<SessionService>(() => SessionService());

  locator.registerLazySingleton<AuthRepository>(() => AuthRepository(locator<ApiClient>(), locator<ConfigService>(), locator<SessionService>()));
  locator.registerLazySingleton<DemoAccessRepository>(() => DemoAccessRepository(locator<ApiClient>(), locator<ConfigService>(), locator<SessionService>()));
  locator.registerLazySingleton<ChatRepository>(() => ChatRepository(locator<ApiClient>(), locator<ConfigService>(), locator<SessionService>()));
  locator.registerLazySingleton<DocumentRepository>(() => DocumentRepository(locator<ApiClient>(), locator<ConfigService>(), locator<SessionService>()));
  locator.registerLazySingleton<ComplianceFilterService>(() => ComplianceFilterService());
  locator.registerLazySingleton<AdminRepository>(() => AdminRepository(locator<ApiClient>(), locator<ConfigService>()));
}


