import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/di/locator.dart';
import '../../core/services/compliance_filter.dart';
import '../../core/services/session_service.dart';
import '../../data/repositories/chat_repository.dart';
import '../../data/repositories/document_repository.dart';
import '../blocs/demo/demo_cubit.dart';

class DemoPage extends StatelessWidget {
  const DemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => DemoCubit(
        locator<ChatRepository>(),
        locator<DocumentRepository>(),
        locator<ComplianceFilterService>(),
        locator<SessionService>(),
      )..initialize(isAdmin: true),
      child: const _DemoView(),
    );
  }
}

class _DemoView extends StatefulWidget {
  const _DemoView();
  @override
  State<_DemoView> createState() => _DemoViewState();
}

class _DemoViewState extends State<_DemoView> {
  final TextEditingController _input = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            _Header(),
            Expanded(child: _Container()),
            _InputRow(controller: _input),
          ],
        ),
      ),
    );
  }
}

class _Header extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: const BoxDecoration(color: Colors.white, border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0)))),
      child: Row(
        children: [
          const Expanded(child: Text('ArionComply Demo', style: TextStyle(color: Color(0xFF059669), fontWeight: FontWeight.w700))),
          BlocBuilder<DemoCubit, DemoState>(builder: (_, s) {
            final label = s.isAdmin
                ? '👑 Admin Mode'
                : 'Session: ${s.sessionCount}/${s.sessionLimit}';
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: s.isAdmin ? const Color(0xFFECFDF5) : const Color(0xFFEFF6FF),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(label),
            );
          }),
        ],
      ),
    );
  }
}

class _Container extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DemoCubit, DemoState>(
      builder: (context, s) {
        if (s.view == DemoView.documents) return const _DocumentGallery();
        if (s.view == DemoView.viewer) return const _DocumentViewer();
        return const _ChatView();
      },
    );
  }
}

class _ChatView extends StatelessWidget {
  const _ChatView();
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (!context.select((DemoCubit c) => c.state.welcomeClosed)) _WelcomeBanner(),
        if (!context.select((DemoCubit c) => c.state.suggestionsHidden)) _Suggestions(),
        Expanded(
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            children: context.select((DemoCubit c) => c.state.messages).map((m) => _MessageTile(m)).toList(),
          ),
        ),
      ],
    );
  }
}

class _WelcomeBanner extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: const Color(0xFFECFDF5), border: Border.all(color: const Color(0xFFA7F3D0)), borderRadius: BorderRadius.circular(8)),
      child: Row(children: [
        const Text('🎉  Welcome to ArionComply Demo!'),
        const Spacer(),
        IconButton(onPressed: () => context.read<DemoCubit>().closeWelcome(), icon: const Icon(Icons.close)),
      ]),
    );
  }
}

class _Suggestions extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final chips = [
      'How to get ISO 27001 certified?',
      'What is a risk register?',
      'ISO 27001 implementation timeline',
      'Statement of Applicability guide',
      'What is GDPR compliance?',
      'How to create ROPA document?',
      'GDPR data mapping requirements',
      'Privacy impact assessment',
    ];
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: const BoxDecoration(color: Color(0xFFF8FAFC), border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0)))),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(children: [
          const Text('💡 Need help getting started? Try these questions:'),
          const Spacer(),
          TextButton(onPressed: () => context.read<DemoCubit>().hideSuggestions(), child: const Text('Hide suggestions')),
        ]),
        const SizedBox(height: 8),
        Wrap(spacing: 8, runSpacing: 8, children: [
          for (final t in chips)
            ActionChip(label: Text(t), onPressed: () => context.read<DemoCubit>().sendMessage(t)),
        ]),
      ]),
    );
  }
}

class _MessageTile extends StatelessWidget {
  final DemoMessage m;
  const _MessageTile(this.m);
  @override
  Widget build(BuildContext context) {
    if (m.sender == 'assistant_rejection') {
      final rej = m.rejection ?? {};
      final topics = List<String>.from(rej['allowedTopics'] ?? const []);
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(color: const Color(0xFFFFF1F2), border: Border.all(color: const Color(0xFFEF4444)), borderRadius: BorderRadius.circular(12)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(rej['title']?.toString() ?? 'Outside Our Expertise', style: const TextStyle(color: Color(0xFFDC2626), fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Text(rej['message']?.toString() ?? ''),
          const SizedBox(height: 8),
          if (topics.isNotEmpty) Wrap(spacing: 6, runSpacing: 6, children: topics.map((t) => OutlinedButton(onPressed: () => context.read<DemoCubit>().sendMessage(t), child: Text(t))).toList()),
        ]),
      );
    }
    final isUser = m.sender == 'user';
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 700),
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isUser ? const Color(0xFF059669) : Colors.white,
          borderRadius: BorderRadius.circular(18),
          border: isUser ? null : Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(m.text, style: TextStyle(color: isUser ? Colors.white : const Color(0xFF334155))),
          if (!isUser && m.quickReplies.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(spacing: 6, runSpacing: 6, children: [
              for (final r in m.quickReplies)
                OutlinedButton(onPressed: () => context.read<DemoCubit>().sendQuickReply(r), child: Text(r)),
            ])
          ]
        ]),
      ),
    );
  }
}

class _InputRow extends StatefulWidget {
  final TextEditingController controller;
  const _InputRow({required this.controller});
  @override
  State<_InputRow> createState() => _InputRowState();
}

class _InputRowState extends State<_InputRow> {
  @override
  Widget build(BuildContext context) {
    final s = context.watch<DemoCubit>().state;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: const BoxDecoration(color: Colors.white, border: Border(top: BorderSide(color: Color(0xFFE2E8F0)))),
      child: Row(children: [
        Expanded(child: TextField(controller: widget.controller, enabled: !s.waiting && !s.limitReached, decoration: const InputDecoration(hintText: 'Ask me about compliance requirements...'))),
        const SizedBox(width: 8),
        ElevatedButton(onPressed: s.waiting || s.limitReached ? null : () { final t = widget.controller.text.trim(); if (t.isNotEmpty) { context.read<DemoCubit>().sendMessage(t); widget.controller.clear(); } }, child: const Text('Send')),
      ]),
    );
  }
}

class _DocumentGallery extends StatelessWidget {
  const _DocumentGallery();
  @override
  Widget build(BuildContext context) {
    final docs = context.watch<DemoCubit>().state.documents;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(children: [
        const Text('📄 Your Generated Documents', style: TextStyle(fontSize: 22, fontWeight: FontWeight.w600)),
        const SizedBox(height: 16),
        LayoutBuilder(builder: (_, c) {
          return Wrap(spacing: 16, runSpacing: 16, children: [
            for (final d in docs)
              InkWell(
                onTap: () => context.read<DemoCubit>().viewDocument(d['id'].toString()),
                child: Container(
                  width: 300,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(color: Colors.white, border: Border.all(color: const Color(0xFFE2E8F0)), borderRadius: BorderRadius.circular(12)),
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Row(children: [Text((d['icon'] ?? '📄') as String, style: const TextStyle(fontSize: 20)), const SizedBox(width: 8), Expanded(child: Text((d['title'] ?? '') as String, style: const TextStyle(fontWeight: FontWeight.w600)))]),
                    const SizedBox(height: 8),
                    Text(d['description']?.toString() ?? '', maxLines: 3, overflow: TextOverflow.ellipsis),
                    const SizedBox(height: 12),
                    Align(alignment: Alignment.centerRight, child: OutlinedButton(onPressed: () => context.read<DemoCubit>().viewDocument(d['id'].toString()), child: const Text('View Document'))),
                  ]),
                ),
              ),
          ]);
        }),
      ]),
    );
  }
}

class _DocumentViewer extends StatelessWidget {
  const _DocumentViewer();
  @override
  Widget build(BuildContext context) {
    final doc = context.watch<DemoCubit>().state.currentDocument ?? {};
    return Column(children: [
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(color: Color(0xFFF8FAFC), border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0)))),
        child: Row(children: [
          OutlinedButton(onPressed: () => context.read<DemoCubit>().backToDocuments(), child: const Text('← Back to Documents')),
          const SizedBox(width: 12),
          Expanded(child: Text((doc['title'] ?? 'Document') as String, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600))),
          TextButton(onPressed: () => context.read<DemoCubit>().backToChat(), child: const Text('Back to Chat')),
          const SizedBox(width: 8),
          ElevatedButton(onPressed: () => context.read<DemoCubit>().closeLeadCapture(), child: const Text('Request Full Access')),
        ]),
      ),
      Expanded(child: SingleChildScrollView(padding: const EdgeInsets.all(24), child: Text((doc['content'] ?? '') as String))),
    ]);
  }
}


