import 'dart:async';
import 'package:flutter/material.dart';
import '../widgets/feature_showcase.dart';
import '../widgets/login_dialog.dart';
import '../widgets/demo_access_dialog.dart';
import '../widgets/privacy_policy_dialog.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  int _currentSlide = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 5), (_) {
      setState(() => _currentSlide = (_currentSlide + 1) % 3);
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF059669), Color(0xFF047857)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.all(Radius.circular(12)),
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Text(
                        'ArionComply Demo',
                        style: TextStyle(color: Colors.white, fontSize: 28, fontWeight: FontWeight.w700),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Revolutionize your compliance workflow with intelligent AI automation that delivers auditable results and complete transparency.',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.white, fontSize: 14, height: 1.4),
                      ),
                      const SizedBox(height: 20),
                      Wrap(
                        spacing: 16,
                        runSpacing: 12,
                        alignment: WrapAlignment.center,
                        children: [
                          ElevatedButton(
                            onPressed: () async {
                              await showDialog(context: context, builder: (_) => const DemoAccessDialog());
                            },
                            style: ElevatedButton.styleFrom(backgroundColor: Colors.white, foregroundColor: const Color(0xFF059669)),
                            child: const Text('Get Started'),
                          ),
                          OutlinedButton(
                            onPressed: () async {
                              await showDialog(context: context, builder: (_) => const LoginDialog());
                            },
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.white,
                              side: const BorderSide(color: Colors.white70, width: 2),
                              backgroundColor: Colors.white.withOpacity(0.15),
                            ),
                            child: const Text('Sign In'),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                FeatureShowcase(currentIndex: _currentSlide, onDotTap: (i) => setState(() => _currentSlide = i)),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () => showDialog(context: context, builder: (_) => const PrivacyPolicyDialog()),
                  child: const Text('Privacy Policy'),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}


