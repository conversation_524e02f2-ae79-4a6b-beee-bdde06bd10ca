{"checksum": "14ed849192bd37702e1402b6c89b134a", "roots": {"bookmark_bar": {"children": [{"date_added": "*****************", "date_last_used": "*****************", "guid": "0d1b29d9-64d8-48df-be9a-d9c7688c3376", "id": "51", "name": "Gmail", "show_icon": false, "source": "sync", "type": "url", "url": "https://accounts.google.com/b/0/AddMailService", "visit_count": 9}, {"date_added": "*****************", "date_last_used": "*****************", "guid": "494f477e-4581-4f66-822e-6fdade6f031d", "id": "52", "name": "Notion Mail", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.notion.so/inbox", "visit_count": 1}, {"date_added": "*****************", "date_last_used": "*****************", "guid": "e6455f7f-234d-4098-b6dd-23b5218e4b45", "id": "53", "name": "(3 unread) - Inbox - Zoho Mail (<EMAIL>)", "show_icon": false, "source": "sync", "type": "url", "url": "https://mail.zoho.com/zm/#mail/folder/inbox", "visit_count": 4}, {"date_added": "*****************", "date_last_used": "*****************", "guid": "23fb5065-93a1-492f-b28b-1ca517b88f4b", "id": "54", "name": "YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://youtube.com/", "visit_count": 7}, {"date_added": "*****************", "date_last_used": "*****************", "guid": "1bfe314c-4ccc-4231-96f9-cbd7ce2747c4", "id": "55", "meta_info": {"power_bookmark_meta": ""}, "name": "YouTube Music", "show_icon": false, "source": "sync", "type": "url", "url": "https://music.youtube.com/", "visit_count": 4}, {"date_added": "13395018445879949", "date_last_used": "0", "guid": "384f9b7f-f3a8-4d71-85bc-39474167b249", "id": "56", "name": "Stremio - Freedom to Stream", "show_icon": false, "source": "sync", "type": "url", "url": "https://web.stremio.com/", "visit_count": 1}, {"date_added": "13398255343889574", "date_last_used": "13400033428334118", "guid": "af1e9f78-f3d7-412e-8adb-e810ef4f8c94", "id": "57", "name": "ATK HUB", "show_icon": false, "source": "sync", "type": "url", "url": "https://hub.atk.pro/", "visit_count": 3}, {"date_added": "13365395637385822", "date_last_used": "13394037184771485", "guid": "8e46458a-94e3-41c6-86d5-c31e6f5be3db", "id": "58", "meta_info": {"power_bookmark_meta": ""}, "name": "The Morgan Housel Podcast | Free Listening on Podbean App", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.podbean.com/podcast-detail/5vs57-2a6395/The-<PERSON>-Housel-Podcast", "visit_count": 0}, {"children": [{"date_added": "13267243923001790", "date_last_used": "0", "guid": "7ef1e86e-94a9-4e14-812e-05e877dabdaf", "id": "60", "name": "RuTracker.org", "show_icon": false, "source": "sync", "type": "url", "url": "https://rutracker.org/forum/index.php", "visit_count": 0}, {"date_added": "13267268827721336", "date_last_used": "13335150978581748", "guid": "1ab8729d-d763-4dab-b9f6-8fcf75239441", "id": "61", "name": "FitGirl Repacks | The ONLY official site for FitGirl Repacks. Every single FG repack installer has a link inside, which leads here. Do not fall for fake and scam sites, which are using my name.", "show_icon": false, "source": "sync", "type": "url", "url": "https://fitgirl-repacks.to/", "visit_count": 0}, {"date_added": "13267835936625898", "date_last_used": "0", "guid": "18805a52-ca76-47bd-ab0d-637937121e79", "id": "62", "name": "Random Test Credit Card Numbers Generator - NamsoGen", "show_icon": false, "source": "sync", "type": "url", "url": "https://namso-gen.com/", "visit_count": 0}, {"date_added": "13276172148461762", "date_last_used": "0", "guid": "26e18fcf-d620-41ac-a6ee-71109bf4fab5", "id": "63", "name": "ultra male perfume - Google Search", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.google.com/search?q=ultra+male+perfume&oq=ultramale+per&aqs=chrome.1.69i57j0i10l2j0i10i457j0i10.9792j0j4&client=ms-android-xiaomi-rev1&sourceid=chrome-mobile&ie=UTF-8", "visit_count": 0}, {"date_added": "13276348865249133", "date_last_used": "0", "guid": "0139d946-4760-47bf-a239-a8dd7a3dad86", "id": "64", "name": "(3016) how to swap left right channels in windows - YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/watch?v=IVoMrq03eMg", "visit_count": 0}, {"date_added": "13390605704342427", "date_last_used": "0", "guid": "7e05bba5-e427-436f-9671-d85955d72395", "id": "65", "name": "Unabridged Audiobooks Free Online", "show_icon": false, "source": "sync", "type": "url", "url": "https://audiobookbay.lu/#", "visit_count": 1}, {"date_added": "13390236269136960", "date_last_used": "0", "guid": "0d7745bf-f7c6-444e-ae6b-3f2a5d8449c9", "id": "66", "name": "LearnFlakes -IT Learning Community", "show_icon": false, "source": "sync", "type": "url", "url": "https://learnflakes.net/?p=home&pid=1", "visit_count": 1}, {"date_added": "13390236278953913", "date_last_used": "0", "guid": "46d5eafc-b10c-4a88-bc1c-e70236efa129", "id": "67", "name": "TorrentDay", "show_icon": false, "source": "sync", "type": "url", "url": "https://torrentday.it/login.php", "visit_count": 1}, {"date_added": "13390271553564984", "date_last_used": "0", "guid": "cecdc42f-38a3-4394-9561-76440d706a48", "id": "68", "name": "Sheet - shadcn/ui", "show_icon": false, "source": "sync", "type": "url", "url": "https://ui.shadcn.com/docs/components/sheet", "visit_count": 0}, {"date_added": "13390274276463386", "date_last_used": "0", "guid": "c85502c4-5932-48cc-93d0-243427e8e53b", "id": "69", "name": "Private Chat – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/chat/stock-transfer-page-FyCMGIGP6XO?b=b_r9T3I6K3isT", "visit_count": 1}], "date_added": "13324450911091378", "date_last_used": "0", "date_modified": "13390605704342427", "guid": "0424980b-1b56-4043-8d07-8641e69c8c49", "id": "59", "name": "Crack Softwares and games", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13293664115310549", "date_last_used": "0", "guid": "366bdd66-19be-48a8-bba6-189ec3866e00", "id": "71", "name": "ETH | Flexpool.io", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.flexpool.io/miner/eth/******************************************", "visit_count": 0}, {"date_added": "13272304090678814", "date_last_used": "0", "guid": "a3210400-7591-45c0-8f68-46e8ad20f068", "id": "72", "name": "2,584.68 ｜ ETHBUSD | Binance Spot", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.binance.com/en/trade/ETH_BUSD?theme=dark&type=spot", "visit_count": 0}, {"date_added": "13272304131371715", "date_last_used": "0", "guid": "b6e0e0da-6bdd-452b-a25b-ebed16f9c842", "id": "73", "name": "WhatToMine - Crypto coins mining profit calculator compared to Ethereum", "show_icon": false, "source": "sync", "type": "url", "url": "https://whattomine.com/coins?aq_380=0&aq_fury=0&aq_470=0&aq_480=0&aq_570=0&aq_580=1&a_580=true&aq_vega56=0&aq_vega64=0&aq_5600xt=0&aq_5700=0&aq_5700xt=0&aq_vii=0&aq_67xt=0&aq_68=0&aq_68xt=0&aq_1050Ti=0&aq_10606=0&aq_1070=0&aq_1070Ti=0&aq_1080=0&aq_1080Ti=0&aq_1660=0&aq_1660Ti=0&aq_166s=0&aq_2060=0&aq_2070=0&aq_2080=0&aq_2080Ti=0&aq_3060=0&aq_3060Ti=0&aq_3070=0&aq_3080=0&aq_3090=0&eth=true&factor%5Beth_hr%5D=30.50&factor%5Beth_p%5D=130.00&e4g=true&factor%5Be4g_hr%5D=30.50&factor%5Be4g_p%5D=130.00&zh=true&factor%5Bzh_hr%5D=21.00&factor%5Bzh_p%5D=110.00&cnh=true&factor%5Bcnh_hr%5D=960.00&factor%5Bcnh_p%5D=115.00&cng=true&factor%5Bcng_hr%5D=760.00&factor%5Bcng_p%5D=120.00&cnr=true&factor%5Bcnr_hr%5D=830.00&factor%5Bcnr_p%5D=130.00&cnf=true&factor%5Bcnf_hr%5D=1650.00&factor%5Bcnf_p%5D=115.00&eqa=true&factor%5Beqa_hr%5D=95.00&factor%5Beqa_p%5D=110.00&cc=true&factor%5Bcc_hr%5D=2.60&factor%5Bcc_p%5D=120.00&cr29=true&factor%5Bcr29_hr%5D=2.40&factor%5Bcr29_p%5D=120.00&ct31=true&factor%5Bct31_hr%5D=0.60&factor%5Bct31_p%5D=110.00&ct32=true&factor%5Bct32_hr%5D=0.16&factor%5Bct32_p%5D=110.00&eqb=true&factor%5Beqb_hr%5D=15.50&factor%5Beqb_p%5D=130.00&rmx=true&factor%5Brmx_hr%5D=470.00&factor%5Brmx_p%5D=90.00&ns=true&factor%5Bns_hr%5D=820.00&factor%5Bns_p%5D=150.00&al=true&factor%5Bal_hr%5D=59.50&factor%5Bal_p%5D=120.00&ops=true&factor%5Bops_hr%5D=4.90&factor%5Bops_p%5D=110.00&eqz=true&factor%5Beqz_hr%5D=14.00&factor%5Beqz_p%5D=120.00&zlh=true&factor%5Bzlh_hr%5D=14.00&factor%5Bzlh_p%5D=110.00&kpw=true&factor%5Bkpw_hr%5D=13.00&factor%5Bkpw_p%5D=170.00&ppw=true&factor%5Bppw_hr%5D=9.40&factor%5Bppw_p%5D=140.00&x25x=true&factor%5Bx25x_hr%5D=0.83&factor%5Bx25x_p%5D=80.00&mtp=true&factor%5Bmtp_hr%5D=0.60&factor%5Bmtp_p%5D=120.00&vh=true&factor%5Bvh_hr%5D=0.44&factor%5Bvh_p%5D=110.00&factor%5Bcost%5D=0.1&sort=Profitability24&volume=0&revenue=24h&factor%5Bexchanges%5D%5B%5D=&factor%5Bexchanges%5D%5B%5D=binance&factor%5Bexchanges%5D%5B%5D=bitfinex&factor%5Bexchanges%5D%5B%5D=bitforex&factor%5Bexchanges%5D%5B%5D=bittrex&factor%5Bexchanges%5D%5B%5D=dove&factor%5Bexchanges%5D%5B%5D=exmo&factor%5Bexchanges%5D%5B%5D=gate&factor%5Bexchanges%5D%5B%5D=graviex&factor%5Bexchanges%5D%5B%5D=hitbtc&factor%5Bexchanges%5D%5B%5D=hotbit&factor%5Bexchanges%5D%5B%5D=ogre&factor%5Bexchanges%5D%5B%5D=poloniex&factor%5Bexchanges%5D%5B%5D=stex&dataset=Main&commit=Calculate", "visit_count": 0}, {"date_added": "13275348260630989", "date_last_used": "0", "guid": "5da986e0-9adc-49d8-b525-eb1b14ac02e2", "id": "74", "name": "101 Gwei | Ethereum Gas Tracker | Etherscan", "show_icon": false, "source": "sync", "type": "url", "url": "https://etherscan.io/gastracker", "visit_count": 0}, {"date_added": "13265945380389546", "date_last_used": "0", "guid": "f9f5803d-ca0e-493e-9ed0-0322b89fe1bd", "id": "75", "name": "ethereum price usd - Google Search", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.google.com/search?q=ethereum+price+usd&rlz=1C1GCEA_enPK951PK951&oq=&aqs=chrome.1.35i39i362l8...8.122187045j0j7&sourceid=chrome&ie=UTF-8", "visit_count": 0}, {"date_added": "13264265873009698", "date_last_used": "0", "guid": "d14acbca-8029-41a1-83ad-4d1f789dcc90", "id": "76", "name": "Dashboard - Ethermine", "show_icon": false, "source": "sync", "type": "url", "url": "https://ethermine.org/miners/d079Adc94a510829D5CC66ae9A045b10d036e782/dashboard", "visit_count": 0}], "date_added": "13324450911094409", "date_last_used": "0", "date_modified": "13324450911094409", "guid": "20854468-f785-4025-9071-031e2a540206", "id": "70", "name": "Crypto", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13274941457371002", "date_last_used": "0", "guid": "edabdbc5-6587-4d7c-9723-82223df59e2b", "id": "78", "name": "SLATE : My Workspace : Overview", "show_icon": false, "source": "sync", "type": "url", "url": "http://slate.nu.edu.pk/portal", "visit_count": 0}, {"date_added": "13274960599285577", "date_last_used": "0", "guid": "8441f5f0-eb56-4543-9451-4884dfd073a7", "id": "79", "name": "Classes", "show_icon": false, "source": "sync", "type": "url", "url": "https://classroom.google.com/u/1/h", "visit_count": 0}, {"date_added": "13276697847411010", "date_last_used": "0", "guid": "fcbf11f6-4160-4cc6-9c38-ec679927f87a", "id": "80", "name": "Desmos | Graphic Cal", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.desmos.com/", "visit_count": 0}, {"date_added": "13277019721777685", "date_last_used": "0", "guid": "cce2a470-37f3-4964-beef-5a4cd57a29d9", "id": "81", "name": "Mathway | Algebra Problem Solver", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.mathway.com/Algebra", "visit_count": 0}, {"date_added": "13285622986740208", "date_last_used": "0", "guid": "3929dff4-5a4f-4203-8a0a-c647a6dbb324", "id": "82", "name": "Python Tutor - Visualize Python, Java, JavaScript, C, C++, Ruby code execution", "show_icon": false, "source": "sync", "type": "url", "url": "https://pythontutor.com/visualize.html#mode=display", "visit_count": 0}, {"date_added": "13288960975742246", "date_last_used": "0", "guid": "dd7cbf05-eaa8-4ee4-9068-22a0b1f504de", "id": "83", "name": "Listening Test - Google Docs", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.google.com/document/d/1mq7EtP4dyru7mle0Gr1MZI_z27mEq-vdMqgi2Jvtpq0/edit", "visit_count": 0}, {"date_added": "13305503246949917", "date_last_used": "13337462157325868", "guid": "e625ab4f-d96f-40c2-93ce-5e3c58f4b56e", "id": "84", "name": "Free PDF, Video, Image & Other Online Tools - TinyWow", "show_icon": false, "source": "sync", "type": "url", "url": "https://tinywow.com/", "visit_count": 0}, {"date_added": "13305527018277413", "date_last_used": "0", "guid": "50089fa6-702f-4cf1-8e8d-87a605dc35af", "id": "85", "name": "Share files privately and fast, without size limit with ToffeeShare", "show_icon": false, "source": "sync", "type": "url", "url": "https://toffeeshare.com/", "visit_count": 0}, {"date_added": "13305527074580840", "date_last_used": "0", "guid": "7bc8df73-a1a7-4a79-aa4a-a16d574b6e33", "id": "86", "name": "Wordtune | Your personal writing assistant & editor", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.wordtune.com/", "visit_count": 0}, {"date_added": "13315259046724680", "date_last_used": "0", "guid": "91517408-448b-4256-bd39-a320bf26bcdd", "id": "87", "name": "https://chat.openai.com/auth/login", "show_icon": false, "source": "sync", "type": "url", "url": "https://chat.openai.com/auth/login", "visit_count": 0}, {"date_added": "13315665741330626", "date_last_used": "0", "guid": "df276649-5677-49e9-919c-0512bffb84c9", "id": "88", "name": "Homeworkify.Net » Unblur , Bartleby Answers Free 2022 | Homework help Online", "show_icon": false, "source": "sync", "type": "url", "url": "https://homeworkify.net/", "visit_count": 0}, {"date_added": "13318118473277034", "date_last_used": "13331676177734411", "guid": "a4dcca81-a6e8-428a-860c-1542cccb2d3a", "id": "89", "name": "Class Central • Find the best courses, wherever they exist.", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.classcentral.com/", "visit_count": 0}, {"date_added": "13319820363951347", "date_last_used": "0", "guid": "7670938f-9a3e-4acc-933b-94c506a718d1", "id": "90", "name": "chat – Figma", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.figma.com/file/es2sP1ClL1QhOPBlwOzHWE/chat?t=JQGGrofQKpalcPxG-0", "visit_count": 0}, {"date_added": "13320994049416083", "date_last_used": "0", "guid": "9030bb47-14f8-4980-9bee-cfe9f7d95550", "id": "91", "name": "Documents", "show_icon": false, "source": "sync", "type": "url", "url": "https://lucid.app/documents#/dashboard", "visit_count": 0}, {"date_added": "13332548813062218", "date_last_used": "0", "guid": "b888e0eb-e8bd-46e7-a396-9f17aaabe67b", "id": "92", "name": "Learn X in Y Minutes: Scenic Programming Language Tours", "show_icon": false, "source": "sync", "type": "url", "url": "https://learnxinyminutes.com/", "visit_count": 0}, {"date_added": "13334528918281263", "date_last_used": "0", "guid": "b6d4587b-fb62-4845-9fd7-e9723cf65277", "id": "93", "name": "Untitled document - Google Docs", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.google.com/document/d/1kFHSdyQBc7Xc3nZRJswNF0Fn7Qf6b1LB19jqFVAibUQ/edit", "visit_count": 0}, {"date_added": "13347932468462557", "date_last_used": "0", "guid": "d3a508b7-be1b-45c1-adec-e03913fb23dc", "id": "94", "meta_info": {"power_bookmark_meta": ""}, "name": "Discover, Create, and Publish your research paper | SciSpace by Typeset", "show_icon": false, "source": "sync", "type": "url", "url": "https://scispace.com/", "visit_count": 0}, {"date_added": "13334790834476012", "date_last_used": "13346276420869073", "guid": "dbf0eb87-2b49-4499-b12d-4301fdc6bdb2", "id": "95", "name": "<PERSON><PERSON> - shape and share your ideas with AI", "show_icon": false, "source": "sync", "type": "url", "url": "https://tome.app/", "visit_count": 0}, {"date_added": "13337461026297997", "date_last_used": "0", "guid": "7cf34846-4f52-4b0b-8d45-55c296379fe9", "id": "96", "name": "Microsoft Designer - Stunning designs in a flash", "show_icon": false, "source": "sync", "type": "url", "url": "https://designer.microsoft.com/?isDalleImage=true&dfsDefaultImages=https%3A%2F%2Fth.bing.com%2Fth%2Fid%2FOIG.2wdZQW6ppksnFfcczMFp%3Fpid%3DImgGn", "visit_count": 0}, {"date_added": "13338379977890005", "date_last_used": "0", "guid": "2dac3519-badd-4048-8ba3-8dc1607a27b1", "id": "97", "name": "Stat%20Modde… - JupyterLab", "show_icon": false, "source": "sync", "type": "url", "url": "https://nb.anaconda.cloud/jupyterhub/user/251c1a95-46a5-4103-8db2-2ff2822defc8/lab/tree/Stat%20Moddeling%20Workshop%201.ipynb", "visit_count": 0}, {"date_added": "13341863797310515", "date_last_used": "0", "guid": "49454bcd-f9d4-4ae8-9191-971565f194fe", "id": "98", "meta_info": {"power_bookmark_meta": ""}, "name": "EE422C MongoDB Atlas Java Tutorial", "show_icon": false, "source": "sync", "type": "url", "url": "https://ayaanmahimwala.notion.site/EE422C-MongoDB-Atlas-Java-Tutorial-52ebda46cb86473880b72548d5b29bd6", "visit_count": 0}, {"date_added": "13345342953807751", "date_last_used": "0", "guid": "a6810122-cdfe-4261-8078-758749a9a5fa", "id": "99", "meta_info": {"power_bookmark_meta": ""}, "name": "ChatGPT", "show_icon": false, "source": "sync", "type": "url", "url": "https://chat.openai.com/c/0ca3d377-04bd-4998-8751-0c44c859c784", "visit_count": 0}, {"date_added": "13336372228646506", "date_last_used": "0", "guid": "fdcfffa1-2394-4523-ab60-6f4861d93cb4", "id": "100", "name": "SQL Injection - Start", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.hacksplaining.com/exercises/sql-injection#/start", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "97b0a6e8-03d7-476c-b5a9-6b0fa6983eaa", "id": "101", "name": "Chatbase | Custom ChatGPT for your data", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.chatbase.co/pricing", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "24f67df7-**************-dd2a6766df98", "id": "102", "name": "Sign in - Google Accounts", "show_icon": false, "source": "sync", "type": "url", "url": "https://accounts.google.com/o/oauth2/v2/auth/oauthchooseaccount?client_id=*************-kq98rctnjtv76ag4ulrfkem43b74poni.apps.googleusercontent.com&redirect_uri=https%3A%2F%2Ffederatedid-na1.services.adobe.com%2Ffederated%2FfromOIDC&state=ARn-PZILPV3mPSxstOwvHnpCb3LeaBzTgLj3pDk6mBhP2hYlunL4Yg4C_yU0sxQrWj-7k9JVwdcF&scope=email%20openid%20profile&response_type=code&service=lso&o2v=2&flowName=GeneralOAuthFlow", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "ffb4bdbf-41dc-437e-81f7-5fa1925fe89e", "id": "103", "name": "Adobe Firefly (Beta)", "show_icon": false, "source": "sync", "type": "url", "url": "https://firefly.adobe.com/inspire/images#", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "7673259b-503d-43e7-a1d6-76096e665125", "id": "104", "name": "#0003 — Triple Column Technique - Mental Health Exercises", "show_icon": false, "source": "sync", "type": "url", "url": "https://mentalhealthexercises.substack.com/p/triple-column-technique", "visit_count": 0}, {"date_added": "13346422341115868", "date_last_used": "0", "guid": "338b2f49-e7a7-4d7a-9883-9e643b1dce4b", "id": "105", "name": "JotBot AI", "show_icon": false, "source": "sync", "type": "url", "url": "https://myjotbot.com/", "visit_count": 0}, {"date_added": "13347500587042796", "date_last_used": "0", "guid": "6bd3bbab-f3c4-4c4b-923f-3541abea7c7a", "id": "106", "meta_info": {"power_bookmark_meta": ""}, "name": "Home - Jasper", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.jasper.ai/?signInWithGoogle=popup", "visit_count": 0}, {"date_added": "13347740800474481", "date_last_used": "0", "guid": "6868f86a-86b4-4484-b484-1cbfc98a2ef4", "id": "107", "meta_info": {"power_bookmark_meta": ""}, "name": "Adobe Firefly", "show_icon": false, "source": "sync", "type": "url", "url": "https://firefly.adobe.com/generate/images?prompt=Close+up+of+detailed+sci-fi+suit%2C+futuristic+render&styles=%7B%22mode%22%3A%22manual%22%2C%22value%22%3A%5B%22art%22%5D%7D&seed=2523&seed=29939&seed=6226&seed=967&aspectRatio=widescreen&dl=en-US&modelInputVersion=v2&guideStrength=50&creativity=6&promptType=external", "visit_count": 0}, {"date_added": "13348178610800198", "date_last_used": "0", "guid": "8331e1d3-a8bc-4b1c-bf5d-86124ae4ad85", "id": "108", "meta_info": {"power_bookmark_meta": ""}, "name": "(7623) <PERSON><PERSON><PERSON>se - YouTube", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.youtube.com/@JimKurose/videos", "visit_count": 0}, {"date_added": "13348484851414896", "date_last_used": "0", "guid": "0c78f338-a106-4940-ad92-06e97f2699e7", "id": "109", "meta_info": {"power_bookmark_meta": ""}, "name": "DhiWise | Build Flutter apps instantly", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.dhiwise.com/flutter/builder/659120593d3d740014c7b496", "visit_count": 0}, {"date_added": "13348641192466834", "date_last_used": "0", "guid": "f5140b04-4ed2-4940-b058-2eac95354d48", "id": "110", "meta_info": {"power_bookmark_meta": ""}, "name": "dart - Very simple image loaded pretty slow with Flutter - Stack Overflow", "show_icon": false, "source": "sync", "type": "url", "url": "https://stackoverflow.com/questions/56434003/very-simple-image-loaded-pretty-slow-with-flutter", "visit_count": 0}, {"date_added": "13345434194742746", "date_last_used": "13345546359628652", "guid": "d2720f5c-8800-4a99-abf9-38236f5bbae2", "id": "111", "meta_info": {"power_bookmark_meta": ""}, "name": "Phind - AI Search Engine and Pair Programmer", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.phind.com/", "visit_count": 0}, {"date_added": "13348792839080544", "date_last_used": "0", "guid": "6e4dbb5e-cc86-4121-ac95-cb24c8dc48cf", "id": "112", "meta_info": {"power_bookmark_meta": ""}, "name": "Flutter-Widget-Of-The-Week/lib/provider/provider_screen.dart at main · axiftaj/Flutter-Widget-Of-The-Week", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/axiftaj/Flutter-Widget-Of-The-Week/blob/main/lib/provider/provider_screen.dart", "visit_count": 0}, {"date_added": "13352511653639793", "date_last_used": "0", "guid": "ddff1cda-2ee8-42ad-bc22-27b6232edd89", "id": "113", "meta_info": {"power_bookmark_meta": ""}, "name": "Recolor Image | Change Clothes Color", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.pixelcut.ai/recolor", "visit_count": 0}, {"date_added": "13353087559770135", "date_last_used": "0", "guid": "b3625210-8746-4db6-adf8-c693af7ff362", "id": "114", "meta_info": {"power_bookmark_meta": ""}, "name": "Whimsical - The iterative workspace for product teams", "show_icon": false, "source": "sync", "type": "url", "url": "https://whimsical.com/", "visit_count": 0}, {"date_added": "13357902646296430", "date_last_used": "0", "guid": "53e3711d-65c8-4042-9298-ed4e67f7db1d", "id": "115", "meta_info": {"power_bookmark_meta": ""}, "name": "Dashboard – FlowCV-Resume", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.flowcv.com/dashboard", "visit_count": 0}, {"date_added": "13364465550757656", "date_last_used": "0", "guid": "2853ee24-d0d5-4389-beaf-9195a638213d", "id": "116", "meta_info": {"power_bookmark_meta": ""}, "name": "Gamma - AI Presentations", "show_icon": false, "source": "sync", "type": "url", "url": "https://gamma.app/create/generate", "visit_count": 0}], "date_added": "13324450911097850", "date_last_used": "0", "date_modified": "13364465550757656", "guid": "6891701e-25f6-430e-abc4-1569393e893f", "id": "77", "name": "UNIVERSITY", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13295473372508889", "date_last_used": "0", "guid": "39560405-577a-40b1-9be8-0e6ac950352b", "id": "118", "name": "Free Icons, <PERSON><PERSON><PERSON> Illustrations, Photos, and Music", "show_icon": false, "source": "sync", "type": "url", "url": "https://icons8.com/", "visit_count": 0}, {"date_added": "13298285962398784", "date_last_used": "0", "guid": "dc97c9b7-f80d-49ed-9dbd-a39f44fb6207", "id": "119", "name": "SmartDraw - Create Flowcharts, Floor Plans, and Other Diagrams on Any Device", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.smartdraw.com/", "visit_count": 0}, {"date_added": "13302490235162270", "date_last_used": "0", "guid": "008f8f3e-972e-435e-bfc3-cfe58038cb85", "id": "120", "name": "Android Asset Studio - Launcher icon generator", "show_icon": false, "source": "sync", "type": "url", "url": "https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html#foreground.type=clipart&foreground.clipart=android&foreground.space.trim=1&foreground.space.pad=0.25&foreColor=rgba(96%2C%20125%2C%20139%2C%200)&backColor=rgb(68%2C%20138%2C%20255)&crop=0&backgroundShape=circle&effects=none&name=ic_launcher", "visit_count": 0}, {"date_added": "13302848090977002", "date_last_used": "0", "guid": "90a6eb62-f36a-4282-95b8-43c9c23fac21", "id": "121", "name": "BWB - Snazzy Maps - Free Styles for Google Maps", "show_icon": false, "source": "sync", "type": "url", "url": "https://snazzymaps.com/style/1268/bwb", "visit_count": 0}, {"date_added": "13302850597019958", "date_last_used": "0", "guid": "b645cbc6-fe13-4f06-8c9d-de0ef7c573ad", "id": "122", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://icon.kitchen/i/H4sIAAAAAAAAA6tWKkvMKU0tVrKqVkpKD0mtKCktSgVxSmBMpcSi9MqcVKXaWh2l3PyU0hyQ4milxLyUovzMFCUdpcz8YiBZnpqkFFsLAKOWvmRPAAAA", "visit_count": 0}, {"date_added": "13303095272944668", "date_last_used": "0", "guid": "982815e3-f273-4b02-9cc3-7998c26b8ae7", "id": "123", "name": "Blush: Illustrations for everyone", "show_icon": false, "source": "sync", "type": "url", "url": "https://blush.design/", "visit_count": 0}, {"date_added": "13304029544234024", "date_last_used": "0", "guid": "8b55e9e3-d2dc-4bb7-92ed-066f92eafe15", "id": "124", "name": "Color Tool - Material Design", "show_icon": false, "source": "sync", "type": "url", "url": "https://material.io/resources/color/#!/?view.left=0&view.right=0", "visit_count": 0}, {"date_added": "13314209972154284", "date_last_used": "0", "guid": "a71b76df-bc39-4bc0-aebb-d5110182e66f", "id": "125", "name": "<PERSON> - <PERSON> Fonts", "show_icon": false, "source": "sync", "type": "url", "url": "https://fonts.google.com/specimen/Sanchez", "visit_count": 0}, {"date_added": "13320513052427821", "date_last_used": "13325938913325290", "guid": "4f7cd01e-5d15-4c74-a830-d32185cc9178", "id": "126", "name": "Instantly parse JSON in any language | quicktype", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.quicktype.io/", "visit_count": 0}, {"date_added": "13321427728826487", "date_last_used": "0", "guid": "a328af35-a7ed-4f20-bc23-a33c8fb4fe82", "id": "127", "name": "Dashboard - WakaTime", "show_icon": false, "source": "sync", "type": "url", "url": "https://wakatime.com/dashboard", "visit_count": 0}, {"date_added": "13323817738340566", "date_last_used": "0", "guid": "ccd1d9b5-0fb5-4314-94f4-0e844c48fa9d", "id": "128", "name": "AUTOMOBILE MANAGEMENT SYSTEM – Figma", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.figma.com/file/sGbC1DNp3hXYDAjdKFPQd4/AUTOMOBILE-MANAGEMENT-SYSTEM?node-id=0-1&t=tnDTeMiytii3Sgcj-0", "visit_count": 0}, {"date_added": "13323845096952262", "date_last_used": "0", "guid": "0fffa26d-b89c-4edf-a25a-53dddacc96ab", "id": "129", "name": "How To Set Up Firebase with Flutter for iOS and Android Apps | DigitalOcean", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.digitalocean.com/community/tutorials/flutter-firebase-setup", "visit_count": 0}, {"date_added": "13330527205182634", "date_last_used": "0", "guid": "491d23a3-5dc2-4123-b6a2-cbaaef9179a0", "id": "130", "name": "Personal apps | Heroku", "show_icon": false, "source": "sync", "type": "url", "url": "https://dashboard.heroku.com/apps", "visit_count": 0}, {"date_added": "13331757643222151", "date_last_used": "0", "guid": "731328dd-5796-4c42-81cc-6d5b3cc63b05", "id": "131", "name": "Projects - FlutterFlow", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.flutterflow.io/dashboard", "visit_count": 0}, {"date_added": "13331819045027196", "date_last_used": "0", "guid": "9ea7b939-8278-4524-a18b-911496b6d2aa", "id": "132", "name": "Most Reliable App & Cross Browser Testing Platform | BrowserStack", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.browserstack.com/", "visit_count": 0}, {"date_added": "13332031442265036", "date_last_used": "0", "guid": "7312a0e2-c00c-4be5-900a-5784679a55cf", "id": "133", "name": "Mahad871 (<PERSON><PERSON>) · GitHub", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/Mahad871", "visit_count": 0}, {"date_added": "13333463210880575", "date_last_used": "0", "guid": "bd8fdae0-e164-4952-b492-e503a256299e", "id": "134", "name": "MX Offroad Master 🕹️ Play on CrazyGames", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.crazygames.com/game/mx-offroad-master", "visit_count": 0}, {"date_added": "13339293520023671", "date_last_used": "13344703761981770", "guid": "cf310b05-78d9-4665-bb8a-a50319a0d108", "id": "135", "name": "DhiWise: App Development Platform for High Productivity", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.dhiwise.com/", "visit_count": 0}, {"date_added": "13346521045972876", "date_last_used": "0", "guid": "814a5a37-2743-4900-a437-fffc136b6e19", "id": "136", "meta_info": {"power_bookmark_meta": ""}, "name": "Realtime Colors", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.realtimecolors.com/?colors=0e1a16-f5faf8-61ad91-adabd3-a482be&fonts=Poppins-Poppins", "visit_count": 0}, {"date_added": "13339203799527141", "date_last_used": "0", "guid": "d0b9b999-e866-4c72-a3cf-df52342a33ba", "id": "137", "name": "FreeApi.app", "show_icon": false, "source": "sync", "type": "url", "url": "https://freeapi.app/", "visit_count": 0}, {"date_added": "13343603460156533", "date_last_used": "0", "guid": "a38e67b3-efea-4ac1-ac65-a8d0e908b9e3", "id": "138", "name": "valorant error code 29 how to fix spread it reddit - Google Search", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.google.com/search?q=belerant+error+code+29+how+to+fix+spread+it+reddit&oq=belerant+error+code+29+how+to+fix+spread+it&gs_lcrp=EgZjaHJvbWUqCQgCECEYChigATIGCAAQRRhAMgkIARAhGAoYoAEyCQgCECEYChigAdIBBi0xajBqOagCALACAA&client=ms-android-google&sourceid=chrome-mobile&ie=UTF-8", "visit_count": 0}, {"date_added": "13346182391401366", "date_last_used": "0", "guid": "69662e8d-4df5-4218-8071-28422b580f10", "id": "139", "name": "Dashboard - Runway", "show_icon": false, "source": "sync", "type": "url", "url": "https://app.runwayml.com/video-tools/teams/msasd871/dashboard", "visit_count": 0}, {"date_added": "13350490530684211", "date_last_used": "0", "guid": "0d226df3-36b3-499f-84c5-3e26ea825660", "id": "140", "meta_info": {"power_bookmark_meta": ""}, "name": "Lucide", "show_icon": false, "source": "sync", "type": "url", "url": "https://lucide.dev/", "visit_count": 0}, {"date_added": "13354214580054706", "date_last_used": "0", "guid": "e91358d3-dd0d-47a2-82a1-a6768be7a10c", "id": "141", "meta_info": {"power_bookmark_meta": ""}, "name": "App Privacy Policy Generator", "show_icon": false, "source": "sync", "type": "url", "url": "https://app-privacy-policy-generator.firebaseapp.com/", "visit_count": 0}, {"date_added": "13354806310318863", "date_last_used": "0", "guid": "d9ee9392-0361-48f4-bd0f-9e7fd44066e1", "id": "142", "meta_info": {"power_bookmark_meta": ""}, "name": "Untitled document - Google Docs", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.google.com/document/d/1OszN1qgWnzugIjm8A1SDmKLszHleuwdpCKfYH3oRvY4/edit", "visit_count": 0}, {"date_added": "13359683736569466", "date_last_used": "0", "guid": "af550850-9ff4-4f0e-9e3a-d3670bda61a6", "id": "143", "meta_info": {"power_bookmark_meta": ""}, "name": "Storage Permission in Android 13 - Flutter - Stack Overflow", "show_icon": false, "source": "sync", "type": "url", "url": "https://stackoverflow.com/questions/75298807/storage-permission-in-android-13-flutter", "visit_count": 0}, {"date_added": "13360876507419934", "date_last_used": "0", "guid": "655d5348-1d56-407d-b1f5-9c049fdea85f", "id": "144", "meta_info": {"power_bookmark_meta": ""}, "name": "White screen | Online Tool", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.whitescreen.online/", "visit_count": 0}, {"date_added": "13364454268766559", "date_last_used": "0", "guid": "718de114-1b1c-49a6-9652-498aed5a0373", "id": "145", "meta_info": {"power_bookmark_meta": ""}, "name": "Appetize.io - Run native mobile apps in your browser", "show_icon": false, "source": "sync", "type": "url", "url": "https://appetize.io/", "visit_count": 0}, {"date_added": "13364454875161066", "date_last_used": "0", "guid": "3e53d800-195f-4c1b-902b-9beac870e456", "id": "146", "meta_info": {"power_bookmark_meta": ""}, "name": "Supernova | The Design System Platform That Grows With You", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.supernova.io/", "visit_count": 0}, {"date_added": "13364615439327064", "date_last_used": "13365392329508230", "guid": "2f57d71b-af76-4d0f-aa31-393ed9ff6408", "id": "147", "meta_info": {"power_bookmark_meta": ""}, "name": "Noun Project: Free Icons & Stock Photos for Everything", "show_icon": false, "source": "sync", "type": "url", "url": "https://thenounproject.com/", "visit_count": 0}, {"date_added": "13375363785543239", "date_last_used": "0", "guid": "ab1c048e-85b7-4d80-8b29-d9e1ecfe6f12", "id": "148", "meta_info": {"power_bookmark_meta": ""}, "name": "Free & Premium Flutter Templates", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.flutterlibrary.com/templates", "visit_count": 1}, {"date_added": "13322930491213059", "date_last_used": "0", "guid": "ee764c93-**************-7884204eef6b", "id": "149", "name": "Photo - Google Photos", "show_icon": false, "source": "sync", "type": "url", "url": "https://photos.google.com/photo/AF1QipMPYGk6Sqc7GYCMghwT6UJjOSbQ78BJa1VykyTr", "visit_count": 0}, {"date_added": "13386533723233457", "date_last_used": "0", "guid": "7b6be81c-1f95-4651-8dd9-9c57ba7b9b1e", "id": "150", "meta_info": {"power_bookmark_meta": ""}, "name": "Expo | IOS APP DEV (No Mac)", "show_icon": false, "source": "sync", "type": "url", "url": "https://expo.dev/", "visit_count": 1}, {"date_added": "13386542934055807", "date_last_used": "0", "guid": "51ddd4a7-bf37-4712-a91c-9439e94d4f63", "id": "151", "meta_info": {"power_bookmark_meta": ""}, "name": "NativeWind | taiwind for React-Native", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.nativewind.dev/", "visit_count": 1}, {"date_added": "13386542980258806", "date_last_used": "0", "guid": "82e517d3-411d-465d-99f5-************", "id": "152", "meta_info": {"power_bookmark_meta": ""}, "name": "Expo Router | Next.js like nav for React-Native", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.expo.dev/router/introduction/", "visit_count": 1}], "date_added": "13324450911102029", "date_last_used": "0", "date_modified": "13386542980258806", "guid": "68341fd9-2437-4dcb-805c-9d04b2a89f30", "id": "117", "name": "App dev", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13351998113751542", "date_last_used": "13398261513233988", "guid": "5c794e37-6adf-41ce-9203-28987a308718", "id": "154", "meta_info": {"power_bookmark_meta": ""}, "name": "shadcn/ui", "show_icon": false, "source": "sync", "type": "url", "url": "https://ui.shadcn.com/", "visit_count": 1}, {"date_added": "13351998287774402", "date_last_used": "13360052824622321", "guid": "3596425f-9d50-43dd-bbd6-d8548b943e16", "id": "155", "meta_info": {"power_bookmark_meta": ""}, "name": "v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/", "visit_count": 0}, {"date_added": "13351998642794540", "date_last_used": "0", "guid": "f50a41a6-b7c5-4866-b556-c58af1076fea", "id": "156", "meta_info": {"power_bookmark_meta": ""}, "name": "Tailwind CSS - Rapidly build modern websites without ever leaving your HTML.", "show_icon": false, "source": "sync", "type": "url", "url": "https://tailwindcss.com/", "visit_count": 0}, {"date_added": "13351998758158461", "date_last_used": "0", "guid": "7e014b0d-6620-48a8-8ff8-3c97d630010a", "id": "157", "meta_info": {"power_bookmark_meta": ""}, "name": "Headless UI - Unstyled, fully accessible UI components", "show_icon": false, "source": "sync", "type": "url", "url": "https://headlessui.com/react/listbox", "visit_count": 0}, {"date_added": "13351998788728442", "date_last_used": "0", "guid": "66eeff9d-a791-46f7-937a-de4bcbdae384", "id": "158", "meta_info": {"power_bookmark_meta": ""}, "name": "Radix <PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.radix-ui.com/", "visit_count": 0}, {"date_added": "13352004456078402", "date_last_used": "13360095347750089", "guid": "10033d86-0624-4ccd-9de3-907001c80922", "id": "159", "meta_info": {"power_bookmark_meta": ""}, "name": "PostHog - The open source Product OS", "show_icon": false, "source": "sync", "type": "url", "url": "https://posthog.com/", "visit_count": 0}, {"date_added": "13353035708839366", "date_last_used": "0", "guid": "50386926-935a-49ee-82f1-b91f1c0b7b9b", "id": "160", "meta_info": {"power_bookmark_meta": ""}, "name": "Vector Icons and Stickers - PNG, SVG, EPS, PSD and CSS", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.flaticon.com/", "visit_count": 0}, {"date_added": "13363473314349059", "date_last_used": "13398710495280261", "guid": "a06dd85d-637b-46de-9162-2c431d94276d", "id": "161", "meta_info": {"power_bookmark_meta": ""}, "name": "Aceternity UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://ui.aceternity.com/", "visit_count": 2}, {"date_added": "13379863255513686", "date_last_used": "13398703051915430", "guid": "fd9d514c-e765-4838-9ab1-ff0051f959f4", "id": "162", "meta_info": {"power_bookmark_meta": ""}, "name": "Bits - React Goodies", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.reactbits.dev/", "visit_count": 3}, {"date_added": "13364615520614911", "date_last_used": "13383004628049902", "guid": "f54821c6-e15e-4df2-8d89-9cc0460e3f1d", "id": "163", "meta_info": {"power_bookmark_meta": ""}, "name": "Noun Project: Free Icons & Stock Photos for Everything", "show_icon": false, "source": "sync", "type": "url", "url": "https://thenounproject.com/", "visit_count": 2}, {"date_added": "13370962368196245", "date_last_used": "0", "guid": "36db0b24-7961-4b7f-933b-0e688a4373e2", "id": "164", "meta_info": {"power_bookmark_meta": ""}, "name": "BuiltWith Technology Lookup", "show_icon": false, "source": "sync", "type": "url", "url": "https://builtwith.com/", "visit_count": 1}, {"date_added": "13377041494359438", "date_last_used": "13383002485479102", "guid": "60f843fe-ecdc-49cd-9265-1f4ca841a847", "id": "165", "meta_info": {"power_bookmark_meta": ""}, "name": "Origin UI - Beautiful UI components built with Tailwind CSS and Next.js", "show_icon": false, "source": "sync", "type": "url", "url": "https://originui.com/", "visit_count": 2}, {"date_added": "13378711201102358", "date_last_used": "0", "guid": "0f981d08-54a7-479f-a280-300d47caefd7", "id": "166", "meta_info": {"power_bookmark_meta": ""}, "name": "Pricing - Builder.io", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.builder.io/m/pricing", "visit_count": 1}, {"date_added": "13379076016411794", "date_last_used": "0", "guid": "7b27ec73-c17c-4db2-8f44-564091057a40", "id": "167", "meta_info": {"power_bookmark_meta": ""}, "name": "Lovable", "show_icon": false, "source": "sync", "type": "url", "url": "https://lovable.dev/", "visit_count": 0}, {"date_added": "13379507378233045", "date_last_used": "0", "guid": "49edad51-5430-4e00-bafb-85de92bd6d48", "id": "168", "meta_info": {"power_bookmark_meta": ""}, "name": "Rombo | Animation library for Tailwind CSS Library", "show_icon": false, "source": "sync", "type": "url", "url": "https://rombo.co/tailwind/", "visit_count": 1}, {"date_added": "13380667638280758", "date_last_used": "0", "guid": "38316b29-ab6a-494f-9b3f-3118e10bfbf1", "id": "169", "meta_info": {"power_bookmark_meta": ""}, "name": "Mantine UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://ui.mantine.dev/", "visit_count": 1}, {"date_added": "13380667727132321", "date_last_used": "0", "guid": "04f416a2-f454-42c5-ac74-1b8007611b29", "id": "170", "meta_info": {"power_bookmark_meta": ""}, "name": "Ant Design - The world's second most popular React UI framework", "show_icon": false, "source": "sync", "type": "url", "url": "https://ant.design/", "visit_count": 1}, {"date_added": "13380671586854296", "date_last_used": "0", "guid": "7338272b-762e-4989-be3c-0404a472cd22", "id": "171", "meta_info": {"power_bookmark_meta": ""}, "name": "MUI: The React component library you always wanted", "show_icon": false, "source": "sync", "type": "url", "url": "https://mui.com/", "visit_count": 1}, {"date_added": "13382555393795542", "date_last_used": "0", "guid": "e4a00c01-0dfd-4175-9275-2a97132870e7", "id": "172", "meta_info": {"power_bookmark_meta": ""}, "name": "Order List – Django REST framework", "show_icon": false, "source": "sync", "type": "url", "url": "http://127.0.0.1:8000/orders/order/", "visit_count": 1}, {"date_added": "13383002172056144", "date_last_used": "0", "guid": "5ff43710-7482-4fab-8483-ba0f4436ee67", "id": "173", "meta_info": {"power_bookmark_meta": ""}, "name": "[bug]: Theme Provider creates hydration error in Next.js 15.0.1 · Issue #5552 · shadcn-ui/ui", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/shadcn-ui/ui/issues/5552#issuecomment-**********", "visit_count": 1}, {"date_added": "13386450132663151", "date_last_used": "0", "guid": "2924bd4b-0ba3-4407-83ff-5008c9471e6e", "id": "174", "meta_info": {"power_bookmark_meta": ""}, "name": "Excalidraw", "show_icon": false, "source": "sync", "type": "url", "url": "https://excalidraw.com/", "visit_count": 1}, {"date_added": "13384972150696342", "date_last_used": "0", "guid": "4ad01c17-a12d-453d-b5fc-41545e219684", "id": "175", "meta_info": {"power_bookmark_meta": ""}, "name": "Rive for Product UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://rive.app/use-cases/product-design", "visit_count": 1}, {"date_added": "13384972200615829", "date_last_used": "0", "guid": "57bf4a47-5c88-415e-83d3-6a107b5557e1", "id": "176", "meta_info": {"power_bookmark_meta": ""}, "name": "Framer: The website builder loved by designers", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.framer.com/?via=van37&gad_source=1&gclid=Cj0KCQiA8fW9BhC8ARIsACwHqYoe-NjIwunL26dKnaTbtuPwlewbIYGA3GMD0tLTJ5Sd1McUzavKvfUaAkAYEALw_wcB", "visit_count": 1}, {"date_added": "13388862080247276", "date_last_used": "0", "guid": "76056ca6-0ecf-49c6-89b2-6a38d2ca9988", "id": "177", "meta_info": {"power_bookmark_meta": ""}, "name": "Data Table - Neobrutalism components", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.neobrutalism.dev/docs/data-table", "visit_count": 1}, {"date_added": "13384972225423201", "date_last_used": "0", "guid": "fbf351c5-1875-48b7-9f5a-b0fae41b6b59", "id": "178", "meta_info": {"power_bookmark_meta": ""}, "name": "unicorn.studio — No-code WebGL Tool", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.unicorn.studio/", "visit_count": 1}, {"date_added": "13389660243219231", "date_last_used": "0", "guid": "74c3c200-cbbb-4105-b4d6-9775faf3d550", "id": "179", "name": "Agora | low latency Conversational AI Agent", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.agora.io/en/products/conversational-ai-engine/?utm_source=announcement-[%E2%80%A6]e-beta-launch&utm_content=introducing-conversational-ai-engine/", "visit_count": 1}, {"date_added": "13384972258951029", "date_last_used": "0", "guid": "c1710ab0-9bd7-484a-901a-ab165416543e", "id": "180", "meta_info": {"power_bookmark_meta": ""}, "name": "Jitter · Fast and simple motion design tool.", "show_icon": false, "source": "sync", "type": "url", "url": "https://jitter.video/", "visit_count": 1}, {"date_added": "13384972277220946", "date_last_used": "0", "guid": "63853dcd-b086-4e30-8084-b0c2c3e0f13c", "id": "181", "meta_info": {"power_bookmark_meta": ""}, "name": "Spline - 3D Design tool in the browser with real-time collaboration", "show_icon": false, "source": "sync", "type": "url", "url": "https://spline.design/", "visit_count": 1}, {"date_added": "13393979169430078", "date_last_used": "0", "guid": "d7bb526e-2d1d-43e2-b423-5bc98563cc8e", "id": "182", "name": "Google Docs", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.google.com/document/u/0/?tgif=d", "visit_count": 1}, {"children": [{"date_added": "13395202638807708", "date_last_used": "13398258902693911", "guid": "e8b7b6d3-fa9a-4cbf-9f94-23460e0ce5ae", "id": "184", "name": "v0 - Modern landing page design – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/modern-landing-page-design-O1soYwqUPXF?b=b_hXGMWD8WhyE", "visit_count": 2}, {"date_added": "13395202684611593", "date_last_used": "13398258952231462", "guid": "3cbc383b-cd7d-4690-95b6-87ac1e129ddf", "id": "185", "name": "v0 - IbraAutomate – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/ibra-automate-2DkFE09An0f?f=1&b=b_S1QDSis48kN", "visit_count": 2}, {"date_added": "13395202692495032", "date_last_used": "13398259092325940", "guid": "54641e5e-1c78-4253-a268-40d1036080e3", "id": "186", "name": "v0 - lunch-box landing page – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/lunch-box-landing-page-pi72178BK2m?b=b_y00WsEmWldY&f=1", "visit_count": 2}, {"date_added": "13395202698182492", "date_last_used": "13398259183822190", "guid": "e837cc75-fee1-4fc0-9ff8-0f1f86d5cba8", "id": "187", "name": "v0 - v0-SFCC | Ecommerce Template – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/v0-sfcc-ecommerce-template-GYwzjanNOrw?b=TtL42M0WRwX&f=1", "visit_count": 2}, {"date_added": "13395202704633793", "date_last_used": "13398259563210043", "guid": "1b25389c-5b57-4471-bd72-eee3260ea6cc", "id": "188", "name": "v0 - <PERSON><PERSON>'s portfolio – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/nihal-s-portfolio-J2GdQyFIHVN?b=b_NHYUmBpYGm3&f=1", "visit_count": 2}, {"date_added": "13395209522900162", "date_last_used": "13398259931121141", "guid": "0c94f7f3-e3d3-4498-9f51-6cad5cc1db49", "id": "189", "name": "v0 - Nexora sim design – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/nexora-sim-design-kN18qBr4qly?f=1&b=b_r4bZyNCDFT3", "visit_count": 2}, {"date_added": "13395209601330622", "date_last_used": "13398259894579800", "guid": "45019411-e3d8-4239-97a2-1a8e75059398", "id": "190", "name": "v0 - NexoraSIM Design – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/nexora-sim-design-epyFXccUxZ1?f=1&b=b_bvRQeHJpCVj", "visit_count": 3}, {"date_added": "13395209750486698", "date_last_used": "13398259626437820", "guid": "73a00730-23a9-449e-99f7-c8a6f9af1cc3", "id": "191", "name": "v0 - Nile University Platform – v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/nile-university-platform-zGKq0ka85yb?b=b_b90UEjmSZJD", "visit_count": 2}, {"date_added": "13398261496866794", "date_last_used": "0", "guid": "bfbf2c01-9c50-47f6-b2ed-22d8148510be", "id": "192", "name": "v0 - Cyberpunk dashboard design - v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/cyberpunk-dashboard-design-v9Hg1dBb5o3?b=b_vCjl2X1Qo6H", "visit_count": 1}, {"date_added": "13396381900119539", "date_last_used": "0", "guid": "543fe679-2e7f-4200-8d86-a4529a464d27", "id": "193", "name": "Ziyana Yachts | Dubai's Luxury Yacht Rental & Boat Charter", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.ziyanayachts.com/ru", "visit_count": 1}, {"date_added": "13398261641972141", "date_last_used": "0", "guid": "eff7e128-6cee-404c-a43c-3adcd49fd247", "id": "194", "name": "v0 - Marketing Website - v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/marketing-website-sV0OtrkXM6x?b=b_HeS3ZMzojJ9", "visit_count": 1}, {"date_added": "13398261902163288", "date_last_used": "0", "guid": "b20ca6b0-6d26-46ce-8628-1a0757c3da3e", "id": "195", "name": "Flowers & Saints: Premium Streetwear | Wear Your Story", "show_icon": false, "source": "sync", "type": "url", "url": "https://flowersandsaints.com.au/", "visit_count": 1}, {"date_added": "13398262417741487", "date_last_used": "0", "guid": "6b7e1308-87bc-4469-bded-c67d7b82efbc", "id": "196", "name": "v0 - Portfolio - v0 by Vercel", "show_icon": false, "source": "sync", "type": "url", "url": "https://v0.dev/community/portfolio-dXeUoYk1mMB?b=b_waK2R4xIKyj", "visit_count": 1}], "date_added": "13395202670841734", "date_last_used": "0", "date_modified": "13398262417741487", "guid": "4e054e2d-1da4-44e8-af4f-7357ce21fa2c", "id": "183", "name": "UI Inspirations", "source": "unknown", "type": "folder"}, {"date_added": "13396659315574946", "date_last_used": "0", "guid": "0436ff62-67cd-4171-8453-cb8085f2f583", "id": "197", "name": "Payload CMS: The Next.js Headless CMS and App Framework", "show_icon": false, "source": "sync", "type": "url", "url": "https://payloadcms.com/", "visit_count": 1}, {"date_added": "13396844207646700", "date_last_used": "13398701752590293", "guid": "230fbf30-9072-4ce5-a402-cfab48c12cc0", "id": "198", "name": "Mvpblocks", "show_icon": false, "source": "sync", "type": "url", "url": "https://blocks.mvp-subha.me/", "visit_count": 2}, {"date_added": "13396844751975672", "date_last_used": "13398702765946371", "guid": "03218f38-0da3-4b04-8371-f0aedada05e5", "id": "199", "name": "Kibo UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.kibo-ui.com/", "visit_count": 2}, {"date_added": "13396844955999551", "date_last_used": "13398699958963150", "guid": "09f74f16-a384-4954-96ec-044753925db0", "id": "200", "name": "Beautiful themes for shadcn/ui — tweakcn | Theme Editor & Generator", "show_icon": false, "source": "sync", "type": "url", "url": "https://tweakcn.com/", "visit_count": 2}, {"date_added": "13397168925241519", "date_last_used": "0", "guid": "d1b4c465-b32d-4ab6-8d40-9077c2481217", "id": "201", "name": "Featured Free Lottie Animations - Curated Motion Designs", "show_icon": false, "source": "sync", "type": "url", "url": "https://lottiefiles.com/featured-free-animations", "visit_count": 1}, {"date_added": "13399355161358420", "date_last_used": "0", "guid": "a8da58ae-6604-449b-a06e-7952da9ff88e", "id": "202", "name": "React marketing components and example pages | Untitled UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.untitledui.com/react/marketing", "visit_count": 1}, {"date_added": "13399791011906458", "date_last_used": "0", "guid": "4497041c-a085-4783-b028-4c6f16d3eff0", "id": "203", "name": "ReUI", "show_icon": false, "source": "sync", "type": "url", "url": "https://reui.io/", "visit_count": 1}, {"date_added": "13399791451049700", "date_last_used": "0", "guid": "76940791-d9dc-436b-b9d3-6d551a3d8270", "id": "204", "name": "Scroll Progress - Motion-Primitives", "show_icon": false, "source": "sync", "type": "url", "url": "https://motion-primitives.com/docs/scroll-progress#", "visit_count": 1}, {"date_added": "13399791630706594", "date_last_used": "0", "guid": "a81ed155-2d64-478d-8678-8f3ad9fdcfda", "id": "205", "name": "Morphing Dialog - Motion-Primitives", "show_icon": false, "source": "sync", "type": "url", "url": "https://motion-primitives.com/docs/morphing-dialog", "visit_count": 1}, {"date_added": "13399792138195046", "date_last_used": "0", "guid": "16b9fcf4-4cb1-4d08-9e03-de228dd0647d", "id": "206", "name": "AI State Loading | KokonutUI - Free UI Components to build beautiful websites", "show_icon": false, "source": "sync", "type": "url", "url": "https://kokonutui.com/docs/components/ai-loading", "visit_count": 1}, {"date_added": "13399792588210074", "date_last_used": "0", "guid": "19ba41df-7dc4-41cb-b91f-ff116b44ae9d", "id": "207", "name": "Image Metadata Preview - React Component | SmoothUI", "show_icon": false, "source": "sync", "type": "url", "url": "https://smoothui.dev/doc/components/image-metadata-preview", "visit_count": 1}, {"date_added": "13399792858690288", "date_last_used": "0", "guid": "c9268061-1441-4c09-aa3a-2dd55da4af4f", "id": "208", "name": "TextureCard · Cult UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.cult-ui.com/docs/components/texture-card", "visit_count": 1}, {"date_added": "13399793358801286", "date_last_used": "0", "guid": "bd39636b-bcb5-4cb3-8ede-c4049bfbaefd", "id": "209", "name": "ShaderLensBlur · Cult UI", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.cult-ui.com/docs/components/shader-lens-blur", "visit_count": 1}], "date_added": "13351998160950360", "date_last_used": "0", "date_modified": "13399793358801286", "guid": "9bfcc705-39b9-42b4-8392-ef9a55214483", "id": "153", "name": "Web App dev", "source": "unknown", "type": "folder"}, {"children": [{"date_added": "13352554929827256", "date_last_used": "13353089009897429", "guid": "55b4543b-32a9-44bb-9d19-f50107de7792", "id": "211", "meta_info": {"power_bookmark_meta": ""}, "name": "Phind", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.phind.com/search?home=true", "visit_count": 0}, {"date_added": "13346521752248896", "date_last_used": "0", "guid": "19647c18-27ec-4dc1-a2f3-fc0beca5be5d", "id": "212", "meta_info": {"power_bookmark_meta": ""}, "name": "Bard", "show_icon": false, "source": "sync", "type": "url", "url": "https://bard.google.com/chat", "visit_count": 0}, {"date_added": "13352332211561284", "date_last_used": "13358185216303506", "guid": "03e6c866-e86a-43b6-8b60-e9b455a88ab2", "id": "213", "meta_info": {"power_bookmark_meta": ""}, "name": "<PERSON>", "show_icon": false, "source": "sync", "type": "url", "url": "https://poe.com/", "visit_count": 0}, {"date_added": "13347745372035606", "date_last_used": "13349136497845818", "guid": "b8941e5a-802b-497d-ad9d-28cf9b4cc65f", "id": "214", "meta_info": {"power_bookmark_meta": ""}, "name": "Perplexity", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.perplexity.ai/", "visit_count": 0}, {"date_added": "13353089080664251", "date_last_used": "0", "guid": "2a0fc8d1-3678-48d4-9d34-216813aafc79", "id": "215", "meta_info": {"power_bookmark_meta": ""}, "name": "pinokio", "show_icon": false, "source": "sync", "type": "url", "url": "https://pinokio.computer/", "visit_count": 0}, {"date_added": "13353089103891617", "date_last_used": "0", "guid": "e40214e1-5e83-49fd-a986-cbfde0cd9f7c", "id": "216", "meta_info": {"power_bookmark_meta": ""}, "name": "openart.ai", "show_icon": false, "source": "sync", "type": "url", "url": "https://openart.ai/", "visit_count": 0}, {"date_added": "13353089645646744", "date_last_used": "0", "guid": "e6fcc15f-a649-4175-b4a0-cf96cf360529", "id": "217", "meta_info": {"power_bookmark_meta": ""}, "name": "SaveDay - AI Bookmark Manager and Web Highlight", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.save.day/", "visit_count": 0}, {"date_added": "13362663437054256", "date_last_used": "0", "guid": "adc06e9f-f740-4253-a2e1-637f57288d28", "id": "218", "meta_info": {"power_bookmark_meta": ""}, "name": "Supermaven: Free AI Code Completion", "show_icon": false, "source": "sync", "type": "url", "url": "https://supermaven.com/", "visit_count": 0}, {"date_added": "13352511608517015", "date_last_used": "13365393412027362", "guid": "f0ea7cc8-3501-45de-bea4-ca477a3b0fab", "id": "219", "meta_info": {"power_bookmark_meta": ""}, "name": "Recolor Image | Change Clothes Color", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.pixelcut.ai/recolor", "visit_count": 0}, {"date_added": "13383568767864504", "date_last_used": "0", "guid": "cbec457c-de29-4eff-a7cf-20edfe44015b", "id": "220", "meta_info": {"power_bookmark_meta": ""}, "name": "Assignment Template - Google Docs", "show_icon": false, "source": "sync", "type": "url", "url": "https://docs.google.com/document/d/1W59QahjHdZMr7STh1m7yfu16WoyopKSOWIhBhCDeCkk/edit?tab=t.0", "visit_count": 1}, {"date_added": "13384008155016564", "date_last_used": "0", "guid": "8861e11a-5f93-4e99-95df-8ed3a539bcee", "id": "221", "meta_info": {"power_bookmark_meta": ""}, "name": "LLM Visualization", "show_icon": false, "source": "sync", "type": "url", "url": "https://bbycroft.net/llm", "visit_count": 1}, {"date_added": "13385066176507360", "date_last_used": "0", "guid": "b0ebe78f-cc96-4d88-b89f-577361987cf4", "id": "222", "meta_info": {"power_bookmark_meta": ""}, "name": "Whiteboard for Online Collaboration | Web Whiteboard", "show_icon": false, "source": "sync", "type": "url", "url": "https://webwhiteboard.com/", "visit_count": 1}, {"date_added": "13385357842412585", "date_last_used": "0", "guid": "555d90e4-019a-4d7d-9e3c-89bbde8ac9e9", "id": "223", "meta_info": {"power_bookmark_meta": ""}, "name": "Convergence – Building a future of abundance", "show_icon": false, "source": "sync", "type": "url", "url": "https://convergence.ai/", "visit_count": 1}], "date_added": "13353089001876476", "date_last_used": "0", "date_modified": "13385357842412585", "guid": "d901af59-9b89-4a99-87ba-a3a045c1efc5", "id": "210", "name": "AI", "source": "unknown", "type": "folder"}, {"date_added": "13378854026394755", "date_last_used": "13398259172024183", "guid": "feeea92b-4877-4f37-8895-81ecf7cf6da7", "id": "224", "meta_info": {"power_bookmark_meta": ""}, "name": "New conversation · GitHub Copilot", "show_icon": false, "source": "sync", "type": "url", "url": "https://github.com/copilot", "visit_count": 3}, {"date_added": "13393178004200967", "date_last_used": "0", "guid": "03d70df8-6432-4f89-a547-84952f6b94ce", "id": "225", "name": "Wix Website Editor | Ziyana Yachts", "show_icon": false, "source": "sync", "type": "url", "url": "https://editor.wix.com/html/editor/web/renderer/edit/1c64a081-c0e4-4897-b09d-d3cea9bbdbe8?metaSiteId=db79bec5-a33c-49dc-a575-41f86ce1138a", "visit_count": 1}], "date_added": "13401209770301415", "date_last_used": "0", "date_modified": "*****************", "guid": "0bc5d13f-2cba-5d74-951f-3f233fe6c908", "id": "1", "name": "Favorites bar", "source": "unknown", "type": "folder"}, "other": {"children": [{"date_added": "13241388078430959", "date_last_used": "0", "guid": "2a6abcb2-de66-45bc-b1d2-451d5a4355d0", "id": "41", "name": "Dashboard ‹ ma<PERSON>'s Blog! — WordPress", "show_icon": false, "source": "sync", "type": "url", "url": "http://127.0.0.1/wordpress/wp-admin/index.php", "visit_count": 0}, {"date_added": "13295599884492679", "date_last_used": "0", "guid": "97d4517c-e9b9-420d-8550-8f1eb0e0986a", "id": "42", "name": "Snapdrop", "show_icon": false, "source": "sync", "type": "url", "url": "https://snapdrop.net/", "visit_count": 0}, {"date_added": "13242896046010637", "date_last_used": "0", "guid": "93b16c87-d47e-4599-b500-bb231314981c", "id": "43", "name": "Namecheap", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.namecheap.com/domains/registration/results/?domain=othisun.com", "visit_count": 0}, {"date_added": "13276034337898476", "date_last_used": "0", "guid": "f8ebfc7b-e6a8-4e8c-bf56-aa9d384bbe67", "id": "44", "name": "Cloud Shell", "show_icon": false, "source": "sync", "type": "url", "url": "https://shell.cloud.google.com/?show=ide%2Cterminal", "visit_count": 0}, {"date_added": "13242896121572101", "date_last_used": "0", "guid": "e5a1cae2-ebea-4b0e-91a3-a6d87ff0ba17", "id": "45", "name": "Domain Names, Websites, Hosting & Online Marketing Tools - GoDaddy PK", "show_icon": false, "source": "sync", "type": "url", "url": "https://pk.godaddy.com/", "visit_count": 0}, {"date_added": "13244930375230543", "date_last_used": "0", "guid": "1ca708c0-78b6-4527-b89c-f0f41b6a469e", "id": "46", "name": "ol", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.coursera.org/payments/finaid?cartId=100814467&specialization=python", "visit_count": 0}, {"date_added": "13249854413309899", "date_last_used": "0", "guid": "41516f67-fdc8-426e-bdbb-a6794c03d0bd", "id": "47", "name": "Programming for Everybody (Getting Started with Python) - Home | Coursera", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.coursera.org/learn/python/home/<USER>", "visit_count": 0}, {"date_added": "13233192686425362", "date_last_used": "0", "guid": "8cb712a0-cbf5-4c6b-ad0d-1c77edae908d", "id": "48", "name": "Stack Overflow - Where Developers Learn, Share, & Build Careers", "show_icon": false, "source": "sync", "type": "url", "url": "https://stackoverflow.com/?newreg=71faf3d74ea7436ea1e2ede56426a296", "visit_count": 0}, {"date_added": "13302760508748944", "date_last_used": "0", "guid": "218f0202-8fcb-4924-a6eb-7d769c22c016", "id": "49", "name": "flutter - How to increase the compile sdk version - Stack Overflow", "show_icon": false, "source": "sync", "type": "url", "url": "https://stackoverflow.com/questions/71094685/how-to-increase-the-compile-sdk-version", "visit_count": 0}, {"date_added": "13295473226600580", "date_last_used": "0", "guid": "43376d2d-164e-47e0-87ec-b5f0cb6e0172", "id": "50", "name": "App Icon Generator", "show_icon": false, "source": "sync", "type": "url", "url": "https://appicon.co/", "visit_count": 0}], "date_added": "13401209770301415", "date_last_used": "0", "date_modified": "13302760508748944", "guid": "82b081ec-3dd3-529c-8475-ab6c344590dd", "id": "2", "name": "Other favorites", "source": "unknown", "type": "folder"}, "synced": {"children": [{"date_added": "13165861514625181", "date_last_used": "0", "guid": "22f149fb-02c4-4975-a6c3-7bce2866c969", "id": "6", "name": "doomed | Synonyms at Dictionary.com Mobile", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.thesaurus.com/browse/doomed", "visit_count": 0}, {"date_added": "13167524800605380", "date_last_used": "0", "guid": "329a68c5-2638-4af2-9a2e-32f760768c25", "id": "7", "name": "<PERSON>: Your brain on video games | TED Talk", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.ted.com/talks/daphne_bavelier_your_brain_on_video_games/up-next#t-648790", "visit_count": 0}, {"date_added": "13182634859606442", "date_last_used": "0", "guid": "a65423f9-e599-4eb3-9eb9-b92d799d7ada", "id": "8", "name": "detail-useful.com", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.detail-useful.com/sale/index/details?id=21933815195674", "visit_count": 0}, {"date_added": "13191785860128764", "date_last_used": "0", "guid": "525d47b5-bc10-47d3-ac0f-4c9286d88c88", "id": "9", "name": "Balance Chemical Equation - Online Balancer - Chemistry Online Education", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.webqc.org/balance.php?reaction=%28NH4%292So4+%2B+NaOH+%3D+NH3+%2B+H2O+%2B+Na2So4", "visit_count": 0}, {"date_added": "13193072694067181", "date_last_used": "0", "guid": "8dfabde1-56d9-48b0-9758-133ad80ace7a", "id": "10", "name": "LiveTracker | Track Any Person", "show_icon": false, "source": "sync", "type": "url", "url": "https://paksim.ga/user/dashboard.php", "visit_count": 0}, {"date_added": "13202738380540512", "date_last_used": "0", "guid": "bc0f52a3-aca4-4433-ad48-194c62f83207", "id": "11", "name": "Login To Love Calculator Prank", "show_icon": false, "source": "sync", "type": "url", "url": "https://getlinks.info/love/findmylink.php", "visit_count": 0}, {"date_added": "13203091507281822", "date_last_used": "0", "guid": "503be137-1da3-4f87-b9f4-7742eac3edd0", "id": "12", "name": "عصابة النمور qiwdz - Register", "show_icon": false, "source": "sync", "type": "url", "url": "http://anomor.com/?do=register", "visit_count": 0}, {"date_added": "13204660646260269", "date_last_used": "0", "guid": "8adf86cf-3eea-48a5-a4fa-b94d804b6056", "id": "13", "name": "Sujood.Co", "show_icon": false, "source": "sync", "type": "url", "url": "http://www.sujood.co/", "visit_count": 0}, {"date_added": "13211120468297357", "date_last_used": "0", "guid": "0859404d-60b1-4722-9c75-7471f815ac86", "id": "14", "name": "Collegepond-Your Future, Our Priority", "show_icon": false, "source": "sync", "type": "url", "url": "http://collegepond.b4live.com/Admin/ViewStudents.aspx", "visit_count": 0}, {"date_added": "13211834869636933", "date_last_used": "0", "guid": "8c94a842-dc00-4a09-9d48-49a96497691a", "id": "15", "name": "Opening a wallet in Yandex.Money | Yandex money", "show_icon": false, "source": "sync", "type": "url", "url": "https://money.yandex.ru/reg?retpath=https%3A%2F%2Fmoney.yandex.ru%2Fvirtualcard%2Fissue", "visit_count": 0}, {"date_added": "13211984068647316", "date_last_used": "0", "guid": "f0b8711a-a189-48c2-aae9-539a5db3a36e", "id": "16", "name": "Moviesdab - download latest movies and tv series", "show_icon": false, "source": "sync", "type": "url", "url": "https://moviesdab.com/", "visit_count": 0}, {"date_added": "13211992752657449", "date_last_used": "0", "guid": "217e0ce1-472b-4e30-9617-2ae223be8bdd", "id": "17", "name": "ᐅ Fake IT - Fake the World | Fake Name & IBAN / Credit Card Generator", "show_icon": false, "source": "sync", "type": "url", "url": "https://fake-it.ws/", "visit_count": 0}, {"date_added": "13216390116758758", "date_last_used": "0", "guid": "cdab09ff-e5bf-464f-b66d-44172795032e", "id": "18", "name": "LiveTracker | All Network Details", "show_icon": false, "source": "sync", "type": "url", "url": "https://paksim.info/search.php?msg=Please%20Enter%20atleast%201%20Mobile%20Number%20or%20CNIC", "visit_count": 0}, {"date_added": "13220460159566665", "date_last_used": "0", "guid": "51b96eaf-6685-40e7-9cee-6c7f9e77fbbb", "id": "19", "name": "Grabify IP Logger & URL Shortener - Logs", "show_icon": false, "source": "sync", "type": "url", "url": "https://grabify.link/logs", "visit_count": 0}, {"date_added": "13224974883739301", "date_last_used": "0", "guid": "d3d1399a-d6d9-4cb7-956d-cd5faf1cc2cf", "id": "20", "name": "QR Code Generator - QR Stuff Free QR Code Generator And Creator.", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.qrstuff.com/", "visit_count": 0}, {"date_added": "13225484355824806", "date_last_used": "0", "guid": "c093e773-7fd4-4004-b615-504190b021fc", "id": "21", "name": "Welcome To Punjab Education Foundation", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.techraj156.com/videos", "visit_count": 0}, {"date_added": "13225485759642749", "date_last_used": "0", "guid": "78f08c22-d4c0-429b-9db3-d0ec2d997c16", "id": "22", "name": "IP Logger URL Shortener - Log and Track IP addresses", "show_icon": false, "source": "sync", "type": "url", "url": "https://iplogger.org/", "visit_count": 0}, {"date_added": "13227530752794319", "date_last_used": "0", "guid": "1817023d-bd08-4585-9d57-f671504c977d", "id": "23", "name": "Mobile Tracker Free | Cell Phone Tracker App | Monitoring App for Android Smartphone", "show_icon": false, "source": "sync", "type": "url", "url": "https://mobile-tracker-free.com/", "visit_count": 0}, {"date_added": "13227990715075472", "date_last_used": "0", "guid": "2adb3e38-4dc5-42c8-afb3-a7b111ed407c", "id": "24", "name": "VirusTotal", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.virustotal.com/gui/", "visit_count": 0}, {"date_added": "13227990730062181", "date_last_used": "0", "guid": "264d7994-cf99-46b8-aa0c-f6275492e762", "id": "25", "name": "Have I Been Pwned: Check if your email has been compromised in a data breach", "show_icon": false, "source": "sync", "type": "url", "url": "https://haveibeenpwned.com/", "visit_count": 0}, {"date_added": "13227990831283174", "date_last_used": "0", "guid": "81baaaac-6861-42ed-b392-c178d52204f0", "id": "26", "name": "1.620038 million+ Stunning Free Images to Use Anywhere - Pixabay", "show_icon": false, "source": "sync", "type": "url", "url": "https://pixabay.com/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "a925fe12-2367-45d1-9349-673c17afa099", "id": "27", "name": "DeviantArt - Discover The Largest Online Art Gallery and Community", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.deviantart.com/", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "********-0414-4ab9-aa8e-98447f0e7e03", "id": "28", "name": "Sign in to your account", "show_icon": false, "source": "sync", "type": "url", "url": "https://login.microsoftonline.com/common/oauth2/authorize?response_type=id_token&client_id=5e3ce6c0-2b1f-4285-8d4b-75ee78787346&redirect_uri=https%3A%2F%2Fteams.microsoft.com%2Fgo&state=d86ac48a-0eb1-44e8-8a86-31d0f7ac4c60&&client-request-id=11d9dcf4-1599-4b26-8b19-49329e967c63&x-client-SKU=Js&x-client-Ver=1.0.9&nonce=d77f755d-a9db-4750-99d5-d24816158ec8&domain_hint=", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "9c17c2a6-514d-44eb-a364-e3a7ebb6b889", "id": "29", "name": "List of Websites That Offer Programming Exercises and Challenges – looperino", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.looperino.com/blogs/coding-advice/list-of-websites-that-offer-programming-exercises-and-challenges", "visit_count": 0}, {"date_added": "*****************", "date_last_used": "0", "guid": "1def388b-cf15-4be0-8618-5b73350d2b20", "id": "30", "name": "reddit: the front page of the internet", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.reddit.com/r/techsupport/comments/e6fm84/sometimes_no_internet_connection_unless_i_restart/?utm_source=amp&utm_medium=&utm_content=post_num_comments", "visit_count": 0}, {"date_added": "13235187728919048", "date_last_used": "0", "guid": "8506b899-60a1-4314-9526-37fb4343489a", "id": "31", "name": "Walls by JFL", "show_icon": false, "source": "sync", "type": "url", "url": "https://sites.google.com/view/wallsbyjfl/home?authuser=0", "visit_count": 0}, {"date_added": "13237623665432562", "date_last_used": "0", "guid": "1ce5e278-e7e4-4ecc-a302-e812e68809b5", "id": "32", "name": "CSS: Zero to Hero in CSS by Styling a Website from Scratch | Udemy", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/course/css-zero-to-hero/learn/lecture/12930518#overview", "visit_count": 0}, {"date_added": "13239318049679964", "date_last_used": "0", "guid": "1f5049e5-3f0f-495d-bf11-bdd026512d78", "id": "33", "name": "<PERSON><PERSON> - Google Fonts", "show_icon": false, "source": "sync", "type": "url", "url": "https://fonts.google.com/specimen/<PERSON><PERSON>+San<PERSON>?sidebar.open&selection.family=<PERSON><PERSON>+Sans:wght@700", "visit_count": 0}, {"date_added": "13240967020585304", "date_last_used": "0", "guid": "58fd9e91-1480-49d4-b5c3-856838dfdc19", "id": "34", "name": "Search Result Page - Free Udemy Courses - DiscUdemy", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.discudemy.com/search", "visit_count": 0}, {"date_added": "13241385856019873", "date_last_used": "0", "guid": "af0e63c8-6d75-494e-8a0d-14d40e217915", "id": "35", "name": "Online Courses - Anytime, Anywhere | Udemy", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.udemy.com/", "visit_count": 0}, {"date_added": "13241835338099397", "date_last_used": "0", "guid": "669d694b-faa1-4d1f-8986-9e36344ac46b", "id": "36", "name": "Hidden Eye – Google Drive", "show_icon": false, "source": "sync", "type": "url", "url": "https://drive.google.com/drive/u/0/mobile/folders/1o0-yqEH5-ex4jKFTZwvmWvh0AgYPrHyA?usp=sharing", "visit_count": 0}, {"date_added": "13251029624356394", "date_last_used": "0", "guid": "c65302a1-06eb-4f8c-ae1a-8112a190745e", "id": "37", "name": "HD wallpaper: digital art, artwork, cyber, cyberpunk, neon, lights, neon lights | Wallpaper Flare", "show_icon": false, "source": "sync", "type": "url", "url": "https://www.wallpaperflare.com/digital-art-artwork-cyber-cyberpunk-neon-lights-neon-lights-wallpaper-udewr", "visit_count": 0}, {"date_added": "13251137056370353", "date_last_used": "0", "guid": "19193588-ea31-4424-b0c4-ba9e2288263e", "id": "38", "name": "Smash", "show_icon": false, "source": "sync", "type": "url", "url": "https://fromsmash.com/H_eLuqT9Gp-dt", "visit_count": 0}, {"date_added": "13348156909480938", "date_last_used": "0", "guid": "42296f98-edd4-41bc-b2aa-3f827dcf30fe", "id": "39", "name": "doyouwannagooutwithme.com/yes.html", "show_icon": false, "source": "sync", "type": "url", "url": "http://doyouwannagooutwithme.com/yes.html", "visit_count": 0}, {"date_added": "13350059492100768", "date_last_used": "0", "guid": "a18e7ca3-094c-4d7c-b02e-79cc70e55226", "id": "40", "name": "Runtime Error", "show_icon": false, "source": "sync", "type": "url", "url": "https://flexstudent.nu.edu.pk/Student/CourseRegistration?dump=29K68mvDfpfc4ZR5Y7lerg%3D%3D", "visit_count": 0}], "date_added": "13401209770301415", "date_last_used": "0", "date_modified": "13350059492100768", "guid": "4cf2e351-0e85-532b-bb37-df045d8f8d0f", "id": "3", "name": "Mobile favorites", "source": "unknown", "type": "folder"}}, "sync_metadata": "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", "version": 1}