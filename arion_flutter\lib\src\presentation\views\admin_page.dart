import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../data/repositories/admin_repository.dart';

class AdminPage extends StatefulWidget {
  const AdminPage({super.key});

  @override
  State<AdminPage> createState() => _AdminPageState();
}

class _AdminPageState extends State<AdminPage> {
  final _email = TextEditingController();
  final _password = TextEditingController();
  final _code = TextEditingController();
  bool _stepMfa = false;
  bool _busy = false;
  String? _error;
  String? _adminToken;
  int _tabIndex = 0; // 0 users, 1 leads
  Map<String, dynamic>? _usersData;
  Map<String, dynamic>? _leadsData;

  AdminRepository get _repo => GetIt.I<AdminRepository>();

  Future<void> _requestCode() async {
    setState(() { _busy = true; _error = null; });
    try {
      final res = await _repo.requestCode(password: _password.text.trim(), email: _email.text.trim());
      if (res['success'] == true) {
        setState(() => _stepMfa = true);
      } else {
        setState(() => _error = (res['error'] ?? 'Invalid admin credentials').toString());
      }
    } catch (e) {
      setState(() => _error = 'Network error');
    } finally {
      setState(() => _busy = false);
    }
  }

  Future<void> _verifyCode() async {
    setState(() { _busy = true; _error = null; });
    try {
      final res = await _repo.verifyCode(code: _code.text.trim());
      if (res['success'] == true) {
        final token = res['data']?['token'] as String?;
        setState(() => _adminToken = token);
        await _loadInitialData();
      } else {
        setState(() => _error = (res['error'] ?? 'Invalid code').toString());
      }
    } catch (e) {
      setState(() => _error = 'Network error');
    } finally {
      setState(() => _busy = false);
    }
  }

  Future<void> _loadInitialData() async {
    setState(() { _busy = true; _error = null; });
    try {
      final users = await _repo.getUsers();
      final leads = await _repo.getLeads();
      setState(() {
        _usersData = users['data'];
        _leadsData = leads['data'];
      });
    } catch (e) {
      setState(() => _error = 'Failed to load admin data');
    } finally {
      setState(() => _busy = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_adminToken == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Admin Login')),
        body: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 420),
            child: Card(
              margin: const EdgeInsets.all(16),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.stretch, children: [
                  Text(_stepMfa ? 'Enter MFA Code' : 'Admin Credentials', style: Theme.of(context).textTheme.titleLarge),
                  const SizedBox(height: 12),
                  if (!_stepMfa) ...[
                    TextField(controller: _email, decoration: const InputDecoration(labelText: 'Admin Email')),
                    const SizedBox(height: 12),
                    TextField(controller: _password, decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
                    const SizedBox(height: 16),
                    ElevatedButton(onPressed: _busy ? null : _requestCode, child: Text(_busy ? 'Verifying...' : 'Continue to MFA')),
                  ] else ...[
                    TextField(controller: _code, decoration: const InputDecoration(labelText: '6-digit Code')),
                    const SizedBox(height: 16),
                    ElevatedButton(onPressed: _busy ? null : _verifyCode, child: Text(_busy ? 'Verifying...' : 'Login')),
                  ],
                  if (_error != null) Padding(padding: const EdgeInsets.only(top: 8), child: Text(_error!, style: const TextStyle(color: Colors.red))),
                ]),
              ),
            ),
          ),
        ),
      );
    }

    return DefaultTabController(
      length: 2,
      initialIndex: _tabIndex,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('ArionComply Admin Dashboard'),
          bottom: const TabBar(tabs: [Tab(text: 'Users'), Tab(text: 'Leads')]),
          actions: [
            TextButton(onPressed: _busy ? null : () async { await _loadInitialData(); }, child: const Text('Refresh', style: TextStyle(color: Colors.white))),
            TextButton(onPressed: _busy ? null : () async { await _repo.cleanupExpiredData(); if (mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Cleanup complete'))); }, child: const Text('Cleanup', style: TextStyle(color: Colors.white))),
          ],
        ),
        body: TabBarView(children: [
          _buildUsers(),
          _buildLeads(),
        ]),
      ),
    );
  }

  Widget _buildUsers() {
    final users = (_usersData?['users'] as List?) ?? [];
    final summary = _usersData?['summary'] as Map? ?? {};
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Wrap(spacing: 16, runSpacing: 16, children: [
          _statCard('Total', summary['total']?.toString() ?? '-'),
          _statCard('Active', summary['active']?.toString() ?? '-'),
          _statCard('Pending', summary['pending']?.toString() ?? '-'),
          _statCard('Suspended', summary['suspended']?.toString() ?? '-'),
        ]),
        const SizedBox(height: 16),
        Expanded(child: _buildDataTable(
          headers: const ['Name', 'Email', 'Company', 'Job Title', 'Status', 'Type', 'Created', 'Actions'],
          rows: users.map<Widget>((u) {
            return _dataRow([
              '${u['first_name']} ${u['last_name']}',
              u['email'] ?? '-',
              u['company_name'] ?? '-',
              u['job_title'] ?? '-',
              u['status'] ?? '-',
              u['type'] ?? '-',
              (u['created_at'] ?? '').toString().split('T').first,
              Row(children: [
                if (u['status'] == 'pending' || u['status'] == 'suspended') TextButton(onPressed: () => _manageUser(u, 'activate'), child: const Text('Activate')),
                if (u['status'] == 'active') TextButton(onPressed: () => _manageUser(u, 'suspend'), child: const Text('Suspend')),
                TextButton(onPressed: () => _manageUser(u, 'delete'), child: const Text('Delete')),
              ]),
            ]);
          }).toList(),
        )),
      ]),
    );
  }

  Widget _buildLeads() {
    final leads = (_leadsData?['leads'] as List?) ?? [];
    final summary = _leadsData?['summary'] as Map? ?? {};
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Wrap(spacing: 16, runSpacing: 16, children: [
          _statCard('Total', summary['total']?.toString() ?? '-'),
          _statCard('SQL', summary['sql']?.toString() ?? '-'),
          _statCard('MQL', summary['mql']?.toString() ?? '-'),
          _statCard('Today', summary['today']?.toString() ?? '-'),
        ]),
        const SizedBox(height: 16),
        Expanded(child: _buildDataTable(
          headers: const ['Name', 'Email', 'Company', 'Job', 'Score', 'Qual.', 'Frameworks', 'Created'],
          rows: leads.map<Widget>((l) {
            return _dataRow([
              '${l['first_name']} ${l['last_name']}',
              l['email'] ?? '-',
              l['company_name'] ?? '-',
              l['job_title'] ?? '-',
              (l['lead_score'] ?? '').toString(),
              (l['lead_qualification_status'] ?? '-').toString(),
              (l['compliance_frameworks'] ?? '-').toString(),
              (l['created_at'] ?? '').toString().split('T').first,
            ]);
          }).toList(),
        )),
      ]),
    );
  }

  Future<void> _manageUser(Map u, String action) async {
    if (_adminToken == null) return;
    setState(() => _busy = true);
    try {
      await _repo.adminManageUser(
        adminToken: _adminToken!,
        userId: (u['id']).toString(),
        userAction: action,
        userType: (u['type']).toString(),
      );
      await _loadInitialData();
    } catch (_) {
      if (mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Action failed')));
    } finally {
      setState(() => _busy = false);
    }
  }

  Widget _statCard(String title, String value) {
    return Card(child: Padding(padding: const EdgeInsets.all(12), child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(value, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
      const SizedBox(height: 4),
      Text(title),
    ])));
  }

  Widget _buildDataTable({required List<String> headers, required List<Widget> rows}) {
    return SingleChildScrollView(
      child: Column(children: [
        Row(children: headers.map((h) => Expanded(child: Padding(padding: const EdgeInsets.all(8), child: Text(h, style: const TextStyle(fontWeight: FontWeight.w600))))).toList()),
        const Divider(height: 1),
        ...rows,
      ]),
    );
  }

  Widget _dataRow(List<dynamic> cells) {
    return Column(children: [
      Row(crossAxisAlignment: CrossAxisAlignment.start, children: cells.map((c) => Expanded(child: Padding(padding: const EdgeInsets.all(8), child: c is Widget ? c : Text(c.toString())))).toList()),
      const Divider(height: 1),
    ]);
  }
}


