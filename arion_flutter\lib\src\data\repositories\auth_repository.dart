import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../core/services/session_service.dart';

class AuthRepository {
  final ApiClient _api;
  final ConfigService _config;
  final SessionService _session;

  AuthRepository(this._api, this._config, this._session);

  Future<Map<String, dynamic>> authenticate(String email, String password) async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['authenticateUser'] ?? 'authenticate-user';
    final res = await _api.post(action, {
      'email': email,
      'password': password,
    }, sessionId: sessionId);
    return Map<String, dynamic>.from(res.data ?? {});
  }

  Future<String> _ensureSession() async {
    var sessionId = await _session.getSessionId();
    if (sessionId == null) {
      sessionId = _session.generateUuid();
      await _session.saveSessionId(sessionId);
    }
    return sessionId;
  }

  Future<Map<String, dynamic>> verifyEmail(String token) async {
    await _config.load();
    final action = _config.actions['verifyEmail'] ?? 'verify-email';
    final res = await _api.post(action, {
      'token': token,
    });
    return Map<String, dynamic>.from(res.data ?? {});
  }
}


