{"aadc_info": {"age_group": 0}, "accessibility": {"captions": {"headless_caption_enabled": false}}, "account_info": [{"access_point": 17, "account_id": "0003BFFD0C579F7E", "accountcapabilities": {"accountcapabilities/g42tslldmfya": -1, "accountcapabilities/g44tilldmfya": -1, "accountcapabilities/ge2dinbnmnqxa": -1, "accountcapabilities/ge2tkmznmnqxa": -1, "accountcapabilities/ge2tknznmnqxa": -1, "accountcapabilities/ge2tkobnmnqxa": -1, "accountcapabilities/ge3dgmjnmnqxa": -1, "accountcapabilities/ge3dgobnmnqxa": -1, "accountcapabilities/geydgnznmnqxa": -1, "accountcapabilities/geytcnbnmnqxa": -1, "accountcapabilities/gezdcnbnmnqxa": -1, "accountcapabilities/gezdsmbnmnqxa": -1, "accountcapabilities/geztenjnmnqxa": -1, "accountcapabilities/gi2tklldmfya": -1, "accountcapabilities/gu2dqlldmfya": -1, "accountcapabilities/gu4dmlldmfya": -1, "accountcapabilities/guydolldmfya": -1, "accountcapabilities/guzdslldmfya": -1, "accountcapabilities/haytqlldmfya": -1, "accountcapabilities/he4tolldmfya": -1}, "edge_account_age_group": 3, "edge_account_cid": "9f008f434aaf6112", "edge_account_environment": 0, "edge_account_environment_string": "login.microsoftonline.com", "edge_account_first_name": "<PERSON><PERSON>", "edge_account_is_test_on_premises_profile": false, "edge_account_last_name": "<PERSON><PERSON>", "edge_account_location": "PK", "edge_account_oid": "", "edge_account_phone_number": "************", "edge_account_puid": "0003BFFD0C579F7E", "edge_account_sovereignty": 0, "edge_account_tenant_id": "9188040d-6c67-4c5b-b112-36a304b66dad", "edge_account_type": 1, "edge_tenant_supports_msa_linking": false, "edge_wam_aad_for_app_account_type": 0, "email": "<EMAIL>", "full_name": "", "gaia": "0003BFFD0C579F7E", "given_name": "", "hd": "", "is_supervised_child": -1, "is_under_advanced_protection": false, "locale": "", "picture_url": ""}], "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "arbitration_last_notification_shown": "*****************", "arbitration_using_experiment_config": false, "autocomplete": {"retention_policy_last_version": 139}, "autofill": {"last_version_deduped": 139, "upload_encoding_seed": "00466746EC0AAE0D14FB3B7295D69C30"}, "bookmark": {"storage_computation_last_update": "*****************"}, "bookmark_bar": {"show_on_all_tabs": true, "show_only_on_ntp": false}, "browser": {"available_dark_theme_options": "All", "chat_v2": {"ip_eligibility_status": {"last_checked_time": "*****************"}}, "copilot_chat_last_used_timestamp": "*****************", "edge_sidebar_visibility": {"_game_assist_": {"order": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": -**********, "4a4878b3-89d5-4dab-8196-4b88da4a3a76": **********, "523b5ef3-0b10-4154-8b62-10b2ebd00921": -**********, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": -715827885, "68604548-9c75-4e8b-89fd-ccc06faa85ad": 1610612735, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": -357913944, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 2147483647, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": -3, "96defd79-4015-4a32-bd09-794ff72183ef": -1073741826, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 1073741823}}, "_gaming_assist_": {"order": {"64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 2147483646, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": -1073741827, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 1073741823}}, "add_app_to_bottom": true, "order": {"168a2510-04d5-473e-b6a0-828815a7ca5f": -16777217, "2caf0cf4-ea42-4083-b928-29b39da1182b": -67108865, "5a99efff-cd77-42d7-86db-f1aebd59ae46": -4194305, "871bba17-fe64-4104-baa1-4426de11d4d2": -2097153, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": -1048577, "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb": -8388609, "e1940e55-5459-4f61-b37f-d7bae6fe5d82": -6291457}}, "edge_sidebar_visibility_debug": {"order_list": ["Search", "Bing Image Creator", "Instagram", "Google Keep", "Tasks", "Google", "YouTube Music", "Drop"], "order_raw_data": {"168a2510-04d5-473e-b6a0-828815a7ca5f": {"name": "Instagram", "pos": "4278190079"}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"name": "Bing Image Creator", "pos": "4227858431"}, "5a99efff-cd77-42d7-86db-f1aebd59ae46": {"name": "Google", "pos": "4290772991"}, "871bba17-fe64-4104-baa1-4426de11d4d2": {"name": "YouTube Music", "pos": "4292870143"}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"name": "Search", "pos": "0"}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"name": "Drop", "pos": "4293918719"}, "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb": {"name": "Google Keep", "pos": "4286578687"}, "e1940e55-5459-4f61-b37f-d7bae6fe5d82": {"name": "Tasks", "pos": "4288675839"}}}, "edge_split_window_last_used_time": "13396041872016540", "editor_proofing_languages": {"en": {"Grammar": false, "Spelling": false}, "en-US": {"Grammar": true, "Spelling": true}}, "enable_text_prediction_v2": true, "hub_app_favicon_data": {"e1940e55-5459-4f61-b37f-d7bae6fe5d82": {"icon_data": "iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAEYElEQVR4nMRXXWgdRRQ+Z2b33tg0V1OaxDRpiia9iYIV8cVqK4aK+hYQVKimD6JvPvSlIDapl/amxReh+CBFEDGIRaTiS9EHa8UHffKngpiYVpvc/NirtrSWJLs7M57Ze/d2d3b3dhuFfJDs3Jmd831nzsw5sxasMyxYZ6xJQLGkNqPtbFGS9+rfiPKS8uzfp0v4J9wiMOuL/cfUABfeKAAbAVD3J7yiyNxPAPK0Z1lvX3gVZ8ODxaNqCIV4JN/CT547gNczC7hrQnXZShyi5sv0Z0M2uKRm0vb4gZ9L+PfAUXcnk/gZ9RdQwTdC8KdmSnj1pgKGyu7DEvAUNbtgbZh3hXzD5qysyYPOsAiWNnNwwnuWyM/8B3JwXdeeXZp7c3l1eSncrxB2Mksc1+3ETeh7ruB9auYThj36+0oBniVXKuSNUoi99H83LeeewOaq4zhzfyx0SiWhcmmp2Ne5pZrP5zsCI+jvmQQBOuZSiVMJ5CQc3hMeP0RLV0kQBv1ltZWDOOx53jCRb9Pk/kSloFJd6ujv7btM1O31EOzXY7EQ1DecuewrJHnv1Jj1Yhq5xvkxnJMgTzjCXVY1BxsQUkD1ypUL5ibkEQ/oqDGl3jX6abVh3/RB6yTcBANl5yEG7HPbsntylg3/LF+PjJO3bOOG1udnXrcuhvpuoHbOo0dNL3uY/METyoaPFE8jh/pub2vdCIXWtsZ4Sz4PPV3dtyPic4aoyM8Rw66rYx4muVYVC8VpcXHo8Op9aeQBOtrbXSQXNPnWzm6P2rchwojhYA06vYIlqoaAL6bHrMd9klAyqY9VmRR7PIatSeQBLl+7ev6OtkI3EW1odHq8I0jbN1aAuT3mZP+oQS2NGuS+g5LxM83ICd+2FwqnI+Ta65w32KBtdCJ2x6YrNV97il0pJJuhCbn0+JP0PGcOCImbYgKaoSXHP9QGITt8cn3UyHtpDnKl3JgAShaLMTOIflh09ap7k0VEg7xmF3tjbzC5EBPALDuWYBDUcNDWBjOIiJD7AkI2gi7u5ioxAb+8hn8R5Q/Gy4/q9JpRRIz8nrLaRiHYBRGn8HtdomMCatrkp4ZRi3L7kXBHiogYuYZQQpfhSL2hBB3hiAiQYE3Sw4Eo9m2f8PaaItDmT1CIyrR3jui2SV4sU1ZFeMGw5aBik+GO2IWEJr5Fj1eM7hV68SUqRh9ABvjkAO+AUVEVquO/HrT3NxXQX1Kd3BI/UvNOY0gXuEnP5uPmfS+Ajrm/7HHPNRYpA+4wL66JV7J62v0S0i8kX9PUs+TRLMUNkWEfheIxMrYbjApbxwqV6eGZsVxs86beCQfL3jP6Ypki4lawQneA0alx6+OkwaaX0nqV+wTi4ciKRfL86STPAzRNxXoiFZwdJFNvTAeyw9EbTse8GblG5g+T7cfU3SjlKG1E/WHyQLIx/E6fc33Upsbxtyx2MwsI496S2iSZ0yN57dOMCVFhMjcfznBZsSYB/yfW/ev4XwAAAP//XRXoOAAAAAZJREFUAwBdY+lC0vqhcQAAAABJRU5ErkJggg==", "update_timestamp": "13400981142272142"}}, "hub_app_non_synced_preferences": {"apps": {"06be1ebe-f23a-4bea-ae45-3120ad86cfea": {"last_path": ""}, "0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": {"last_path": ""}, "168a2510-04d5-473e-b6a0-828815a7ca5f": {"last_path": ""}, "1ec8a5a9-971c-4c82-a104-5e1a259456b8": {"last_path": ""}, "2354565a-f412-4654-b89c-f92eaa9dbd20": {"last_path": ""}, "25fe2d1d-e934-482a-a62f-ea1705db905d": {"last_path": ""}, "2caf0cf4-ea42-4083-b928-29b39da1182b": {"last_path": ""}, "35a43603-bb38-4b53-ba20-932cb9117794": {"last_path": ""}, "380c71d3-10bf-4a5d-9a06-c932e4b7d1d8": {"last_path": ""}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"last_path": ""}, "523b5ef3-0b10-4154-8b62-10b2ebd00921": {"last_path": ""}, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": {"last_path": ""}, "698b01b4-557a-4a3b-9af7-a7e8138e8372": {"last_path": ""}, "76b926d6-3738-46bf-82d7-2ab896ddf70b": {"last_path": ""}, "7b52ae05-ae84-4165-b083-98ba2031bc22": {"last_path": ""}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"last_path": ""}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"last_path": ""}, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": {"last_path": ""}, "96defd79-4015-4a32-bd09-794ff72183ef": {"last_path": ""}, "a1a78183-6db3-4789-9e7c-84d157846d55": {"last_path": ""}, "bacc3c12-ebff-44b4-a0f8-ce8b69c9e047": {"last_path": ""}, "c814ae4d-fa0a-4280-a444-cb8bd264828b": {"last_path": ""}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"last_path": ""}, "d3ff4c56-a2b8-4673-ad13-35e7706cc9d1": {"last_path": ""}, "da15ec1d-543d-41c9-94b8-eb2bd060f2c7": {"last_path": ""}, "dadd1f1c-380c-4871-9e09-7971b6b15069": {"last_path": ""}, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": {"last_path": ""}}}, "hub_app_preferences": {"2cb2db96-3bd0-403e-abe2-9269b3761041": {"auto_show": {"enabled": true}}, "439642fc-998d-4a64-8bb6-940ecaf6b60b": {"auto_show": {"enabled": true}}, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": {"all_scenarios": {"auto_open": {"enabled": true}}, "auto_show": {"enabled": true}, "f5b8c725-cb2e-4c12-accd-73e500d88d47": {"auto_show": {"enabled": true}}}, "895f0fc4-d4db-4023-9045-0f0b5cf35e9e": {"is_pinned": true}, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": {"auto_show": {"enabled": true}}, "_game_assist_": {"user_generated_index": ["68604548-9c75-4e8b-89fd-ccc06faa85ad", "4a4878b3-89d5-4dab-8196-4b88da4a3a76"]}, "cd4688a9-e888-48ea-ad81-76193d56b1be": {"auto_show": {"enabled": true}, "is_pinned": true}, "d304175e-bbd4-4b66-8839-9627e56f391f": {"auto_show": {"enabled": true}}, "default_on_apps_cleanup_state": 1, "game_assist_apps_initialized": true, "user_added_trusted": ["b6eaf8c9-8204-4dd0-8587-22b76ba0baeb", "5a99efff-cd77-42d7-86db-f1aebd59ae46", "168a2510-04d5-473e-b6a0-828815a7ca5f", "871bba17-fe64-4104-baa1-4426de11d4d2", "92f1b743-e26b-433b-a1ec-912d1f0ad1fa", "e1940e55-5459-4f61-b37f-d7bae6fe5d82"], "user_generated": {"5a99efff-cd77-42d7-86db-f1aebd59ae46": {"device_emulation": "mobile_touch", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/google.com.png", "id": "5a99efff-cd77-42d7-86db-f1aebd59ae46", "name": "Google", "notificationsEnabled": true, "preferred_side_pane_width": 376, "url": "https://www.google.com/"}, "871bba17-fe64-4104-baa1-4426de11d4d2": {"device_emulation": "none", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/music.youtube.com.png", "id": "871bba17-fe64-4104-baa1-4426de11d4d2", "name": "YouTube Music", "notificationsEnabled": true, "preferred_side_pane_width": 376, "url": "https://music.youtube.com/"}, "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb": {"device_emulation": "none", "icon_url": "https://static.edge.microsoftapp.net/consumer/edgeml/sai/SAI_favicon_v2/keep.google.com.png", "id": "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb", "name": "Google Keep", "notificationsEnabled": true, "preferred_side_pane_width": 376, "url": "https://keep.google.com/"}, "e1940e55-5459-4f61-b37f-d7bae6fe5d82": {"device_emulation": "mobile_touch", "icon_url": "edge://favicon2/?size=32&scaleFactor=2x&pageUrl=https://tasks.google.com/tasks/&showFallbackMonogram=1", "id": "e1940e55-5459-4f61-b37f-d7bae6fe5d82", "name": "Tasks", "navigable": false, "notificationsEnabled": true, "url": "https://tasks.google.com/tasks/"}}, "user_generated_index": ["b6eaf8c9-8204-4dd0-8587-22b76ba0baeb", "5a99efff-cd77-42d7-86db-f1aebd59ae46", "871bba17-fe64-4104-baa1-4426de11d4d2", "e1940e55-5459-4f61-b37f-d7bae6fe5d82"]}, "hub_app_usage_preferences": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 2, "168a2510-04d5-473e-b6a0-828815a7ca5f": 4, "2caf0cf4-ea42-4083-b928-29b39da1182b": 5, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 1, "529de4f7-b6c4-4c76-b36d-c511c9328ebe": 100, "5a99efff-cd77-42d7-86db-f1aebd59ae46": 10, "CleanupCounts": 1, "OpenFirstTime": 1684753335, "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb": 19, "cd4688a9-e888-48ea-ad81-76193d56b1be": 398}, "hub_apps_tower_visible": true, "hub_cleanup_candidate_list_for_debug": [{"cleanup_progress": "cleanup_start_v2"}, {"cleanup_progress": "skipped_cleanup_v2_has_happened"}], "hub_cleanup_context": {"cleanup_last_time_v3": 1730889113.439759, "show_days": "01000000000000000000000000000111", "sidebar_show_last_time": 3720766}, "hub_cleanup_context_v2": {"cleanup_debug_info_v2_adjusted_engaged_app_count": 3, "cleanup_debug_info_v2_app_count_threshold": 1, "cleanup_debug_info_v2_current_sidebar_visibility": 0, "cleanup_debug_info_v2_discover_icon_enabled": true, "cleanup_debug_info_v2_dwell_time_in_secs": 10, "cleanup_debug_info_v2_engaged_app_count": 0, "cleanup_debug_info_v2_expected_sidebar_visibility": 1, "cleanup_debug_info_v2_is_tower_off_by_user": false, "cleanup_debug_info_v2_skip_user_generated_apps_for_threshold": true, "cleanup_debug_info_v2_user_generated_app_count": 3, "hub_app_cleanup_v2_done": true, "sidebar_autohide_by_cleanup_v2": true, "sidebar_cleanup_v2_pref_system_modified": true}, "hub_toggle_time": "13401209781244815", "recent_theme_color_list": [4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0, 4293914607.0], "show_edge_feedback_smiley_button": false, "show_edge_split_window_toolbar_button": false, "show_hub_app_in_sidebar_buttons": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "168a2510-04d5-473e-b6a0-828815a7ca5f": 2, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 0, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "5a99efff-cd77-42d7-86db-f1aebd59ae46": 2, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "871bba17-fe64-4104-baa1-4426de11d4d2": 2, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 2, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "_game_assist_": {"4a4878b3-89d5-4dab-8196-4b88da4a3a76": 2, "68604548-9c75-4e8b-89fd-ccc06faa85ad": 2, "e6723537-66ff-4f4e-ab56-a4cbaddf4e0f": 2}, "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb": 2, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "d304175e-bbd4-4b66-8839-9627e56f391f": 3, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3, "e1940e55-5459-4f61-b37f-d7bae6fe5d82": 2}, "show_hub_app_in_sidebar_buttons_legacy": {"0c835d2d-9592-4c7a-8d0a-0e283c9ad3cd": 3, "168a2510-04d5-473e-b6a0-828815a7ca5f": 2, "2354565a-f412-4654-b89c-f92eaa9dbd20": 0, "2caf0cf4-ea42-4083-b928-29b39da1182b": 0, "523b5ef3-0b10-4154-8b62-10b2ebd00921": 3, "5a99efff-cd77-42d7-86db-f1aebd59ae46": 2, "64be4f9b-3b81-4b6e-b354-0ba00d6ba485": 3, "76b926d6-3738-46bf-82d7-2ab896ddf70b": 3, "8682d0fa-50b3-4ece-aa5b-e0b33f9919e2": 3, "871bba17-fe64-4104-baa1-4426de11d4d2": 2, "8ac719c5-140b-4bf2-a0b7-c71617f1f377": 0, "92f1b743-e26b-433b-a1ec-912d1f0ad1fa": 2, "96defd79-4015-4a32-bd09-794ff72183ef": 3, "b6eaf8c9-8204-4dd0-8587-22b76ba0baeb": 2, "cd4688a9-e888-48ea-ad81-76193d56b1be": 0, "dadd1f1c-380c-4871-9e09-7971b6b15069": 3, "e1940e55-5459-4f61-b37f-d7bae6fe5d82": 2}, "show_hub_app_in_sidebar_buttons_legacy_update_time": "13401209895185145", "show_hub_apps_tower_pinned": false, "show_toolbar_bookmarks_button": true, "show_toolbar_collections_button": false, "show_toolbar_history_button": true, "show_toolbar_share_button": true, "time_of_last_normal_window_close": "13401209954376151", "toolbar_browser_essentials_button_pinned": true, "underside_chat_bing_signed_in_status": false, "underside_chat_consent": 1, "underside_copilot_consent": 1, "user_level_features_context": {}, "window_placement": {"bottom": 821, "left": 405, "maximized": false, "right": 1456, "top": 24, "work_area_bottom": 816, "work_area_left": 0, "work_area_right": 1536, "work_area_top": 0}}, "browser_content_container_height": 708, "browser_content_container_width": 1036, "browser_content_container_x": 0, "browser_content_container_y": 81, "browser_essentials": {"last_used_time": "13394496868475726", "show_hub_fre": false, "show_safety_fre": false}, "collections": {"prism_collection": {"enroll": {"rule_version": 1, "state": 2}}, "prism_collections": {"enabled": 0, "migration": {"accepted": true, "completed": 2, "item_count": 0}, "policy": {"cached": 2}, "wns": {"last_subscribe_time": "13401209866416876", "subscription_id": "1;7224490413518882308"}}}, "commerce_daily_metrics_last_update_time": "13401209770493660", "continuous_migration": {"active_guid": "", "equal_opt_out_users_data": {"backfilled": true, "detected": true, "disable_autolaunch": true}}, "copilot_vision": {"user_access": true}, "countryid_at_install": 21843, "credentials_enable_breachdetection": true, "credentials_enable_service": true, "custom_links": {"list": [{"isMostVisited": false, "policyLevel": 0, "position": 0, "title": "(221) YouTube", "url": "https://www.youtube.com/"}, {"isMostVisited": false, "policyLevel": 0, "position": 1, "title": "Web Store", "url": "https://chrome.google.com/webstore?hl=en"}, {"isMostVisited": false, "policyLevel": 0, "position": 2, "title": "QURAN", "url": "https://archive.org/details/Quran<PERSON><PERSON><PERSON>-15Lines-PakistaniPrint/page/n161"}, {"isMostVisited": false, "policyLevel": 0, "position": 3, "title": "Music", "url": "https://mp3skulls.to/"}, {"isMostVisited": false, "policyLevel": 0, "position": 4, "title": "music", "url": "https://ytmp3.cc/"}, {"isMostVisited": false, "policyLevel": 0, "position": 5, "title": "https://rarbg.to", "url": "https://rarbg.to/"}, {"isMostVisited": false, "policyLevel": 0, "position": 6, "title": "SUBTITLES", "url": "https://www.tv-subs.net/tvshow-2742-2.html"}, {"isMostVisited": false, "policyLevel": 0, "position": 7, "title": "SUBTITLES", "url": "https://subdl.com/subtitle_details.php?p=la-casa-de-papel-third-season"}]}, "devtools": {"preferences": {"EdgeDevToolsLayoutInfo": {"current_dock_state": 0, "horizontal_size": 300, "showEmulationMode": false, "vertical_size": 555}}}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "dual_engine": {"consumer_site_list_with_ie_entries": false, "consumer_sitelist_location": "", "consumer_sitelist_version": "", "external_consumer_shared_cookie_data": {}, "shared_cookie_data": {}, "sitelist_has_consumer_data": false, "sitelist_has_enterprise_data": false, "sitelist_location": "", "sitelist_source": 0, "sitelist_version": "", "user_list_data_1": {}}, "edge": {"account_type": 1, "auto_grouping": {"applied_suggestions_tutorial_shown": true, "partial_notification_last_clicked_time": "*****************", "seen_suggestions_in_bubble": true, "tutorial_shown": true}, "bookmarks": {"last_dup_info_record_time": "*****************"}, "hub_app_store": {"banner_statistic": {}}, "msa_sso_info": {"allow_for_non_msa_profile": false}, "profile_matches_os_primary_account": false, "profile_sso_info": {"aad_sso_algo_state": 1, "is_first_profile": true, "is_msa_first_profile": true, "msa_sso_algo_state": 2, "msa_sso_state_reached_by": 3}, "profile_sso_option": 1, "services": {"last_gaia_id": "0003BFFD0C579F7E", "signin_scoped_device_id": "649110b7-1eda-4eda-9ec2-bb84f45ef6eb"}, "vertical_tabs": {"used_in_relevant_flow": true}, "workspaces": {"state": "{\"edgeWorkspacePrefsVersion\":2,\"enableFluid\":true,\"failedRestartSpaceId\":\"\",\"failedToConnectToFluid\":false,\"fluidStatus\":0,\"fre_shown\":false,\"fromCache\":false,\"isFluidPreferencesConnected\":false,\"isSpaceOpening\":false,\"openingSpaceId\":\"\",\"statusForScreenReaders\":\"\",\"workspacePopupMode\":0,\"workspacesForExternalLinks\":[]}", "storage": {"state": "{\"app_folder_path\":\"\",\"container_id\":\"\",\"drive_id\":\"\",\"prefs_item_id\":\"\",\"storage_endpoint\":\"\",\"version\":3}"}}}, "edge_cloud_messaging": {"cached_target_token": {"cv": "1014901463836528642", "target_token": "fjhDCsBMfQSWjhJbrK2JxA==$E3WKfIZt9aIBlVgHAFgPNTEOyc5A3D/K3kPDuIRmTBg0sdzY1mamWO9qo2hMJV4izZHVoZRNaRtClD8xl0sDUem6PxtFiCgqdM0crEQiZkb2etcxMNrcEKEix3UnZ4KtdDIbOPLP9E6vQFrG3SoX407sVKyilI6uLG4z/vXTJZ4=", "time": "13401209771472338"}}, "edge_quick_search": {"customized_disabled_sites": ["www.pornhub.com", "chatgpt.com", "www.udemy.com", "localhost"]}, "edge_rewards": {"cache_data": "CAEQngMYAEoCcGs=", "hva_promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activitymax": "-1", "activityprogress": "-1", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/MusicNote.json", "base_sku_sweepstake_entries": "0", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Spotify_free3m_593x303.png", "complete": "False", "description": "", "destination": "", "end_of_week": "2025-09-07T23:59:59.9999999-07:00", "free_sweepstake_entries": "1", "give_eligible": "False", "hidden": "True", "hva_sweepstake_entries": "0", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/64x64/BingL<PERSON>-white-icon-64x64px.png", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-dark-teal-278x180px.png", "in_progress_entries": "0", "lastactivitydate": "0001-01-01T00:00:00.0000000+00:00", "link_text": "", "max": "-1", "mobile_sweepstakes_entries_this_week": "0", "offerid": "", "official_faq_link": "https://promos.microsoftrewards.com/US/en/IW/14/faqs", "official_rules_link": "https://promos.microsoftrewards.com/US/en/MS/12/rules", "progress": "-1", "redeemed_base_highvaluesweepstakes_sku": "False", "redeemed_base_highvaluesweepstakes_sku_order_state": "", "redeemed_sweepstake_entries": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Spotify_free3m_1083x609.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Spotify_free3m_1600x600.png", "share_and_win_sweepstake_entries": "0", "skip_force_sms_challenge": "False", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/Star-dark-teal-75x75px.png", "start_of_week": "2025-09-01T00:00:00.0000000-07:00", "sweepstake_version": "v3", "sweepstakes_entries_this_week": "0", "task10_100pts_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task10_100pts_completion_status": "False", "task10_100pts_progress_status": "0", "task10_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task10_activation_status": "False", "task10_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task10_completion_status": "False", "task10_entries_max_num": "20", "task10_progress_status": "0", "task11_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task11_activation_status": "False", "task11_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task11_completion_status": "False", "task11_progress_status": "0", "task12_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task12_completion_status": "False", "task12_progress_status": "-1", "task13_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task13_activation_status": "False", "task13_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task13_completion_status": "False", "task13_progress_status": "-1", "task14_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task14_activation_status": "False", "task14_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task14_completion_status": "False", "task14_progress_status": "-1", "task15_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task15_activation_status": "False", "task15_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task15_completion_status": "False", "task15_progress_status": "-1", "task16_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task16_activation_status": "False", "task16_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task16_completion_status": "False", "task16_progress_status": "0", "task17_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task17_completion_status": "False", "task17_progress_status": "0", "task18_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task18_completion_status": "False", "task18_progress_status": "-1", "task19_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task19_activation_status": "False", "task19_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task19_completion_status": "False", "task19_progress_status": "-1", "task1_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task1_activation_status": "False", "task1_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task1_completion_status": "False", "task1_progress_status": "0", "task20_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task20_activation_status": "False", "task20_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task20_completion_status": "False", "task20_progress_status": "-1", "task21_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task21_activation_status": "False", "task21_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task21_completion_status": "False", "task21_progress_status": "-1", "task22_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task22_completion_status": "False", "task22_progress_status": "-1", "task23_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task23_completion_status": "False", "task23_progress_status": "-1", "task24_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task24_activation_status": "False", "task24_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task24_completion_status": "False", "task24_progress_status": "0", "task25_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task25_activation_status": "False", "task25_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task25_completion_status": "False", "task25_progress_status": "-1", "task26_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task26_activation_status": "False", "task26_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task26_completion_status": "False", "task26_progress_status": "-1", "task27_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task27_activation_status": "False", "task27_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task27_completion_status": "False", "task27_progress_status": "-1", "task28_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task28_activation_status": "False", "task28_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task28_completion_status": "False", "task28_progress_status": "0", "task29_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task29_activation_status": "False", "task29_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task29_completion_status": "False", "task29_progress_status": "-1", "task2_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task2_activation_status": "False", "task2_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task2_completion_status": "False", "task2_progress_status": "0", "task30_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task30_activation_status": "False", "task31_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task31_activation_status": "False", "task31_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task31_completion_status": "False", "task31_progress_status": "-1", "task33_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task33_activation_status": "False", "task33_completion_currentweek_date": "9999-12-31T23:59:59.9999999+00:00", "task33_completion_currentweek_status": "False", "task33_completion_totalweeks_date": "3155378975999999999", "task33_completion_totalweeks_status": "0", "task33_entries_max_num": "50", "task33_last_search_date": "0001-01-01T00:00:00.0000000+00:00", "task33_progress_currentweek_status": "-1", "task33_progress_totalweeks_status": "-1", "task34_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task34_activation_status": "False", "task34_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task34_completion_status": "False", "task34_progress_status": "-1", "task36_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task36_activation_status": "False", "task36_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task36_completion_status": "False", "task36_progress_status": "0", "task37_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task37_activation_status": "False", "task37_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task37_completion_status": "False", "task37_progress_status": "0", "task38_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task38_activation_status": "False", "task38_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task38_completion_status": "False", "task38_progress_status": "0", "task39_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task39_activation_status": "False", "task39_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task39_completion_status": "False", "task39_progress_status": "0", "task3_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task3_activation_status": "False", "task3_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task3_completion_status": "False", "task3_progress_status": "-1", "task40_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task40_activation_status": "False", "task40_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task40_completion_status": "False", "task40_progress_status": "0", "task41_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task41_activation_status": "False", "task41_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task41_completion_status": "False", "task41_progress_status": "0", "task43_activation_status": "False", "task43_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task43_completion_status": "False", "task43_progress_status": "0", "task44_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task44_activation_status": "False", "task44_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task44_completion_status": "False", "task44_progress_status": "-1", "task45_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task45_activation_status": "False", "task45_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task45_completion_status": "False", "task45_progress_status": "-1", "task46_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task46_activation_status": "False", "task46_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task46_completion_status": "False", "task46_progress_status": "-1", "task4_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task4_activation_status": "False", "task4_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task4_completion_status": "False", "task4_progress_status": "-1", "task5_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task5_activation_status": "False", "task5_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task5_completion_status": "False", "task5_progress_status": "-1", "task6_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task6_activation_status": "False", "task6_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task6_completion_status": "False", "task6_progress_status": "-1", "task7_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task7_activation_status": "False", "task7_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task7_completion_status": "False", "task7_progress_status": "-1", "task8_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task8_activation_status": "False", "task8_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task8_completion_status": "False", "task8_progress_status": "-1", "task9_activation_date": "9999-12-31T23:59:59.9999999+00:00", "task9_activation_status": "False", "task9_completion_date": "9999-12-31T23:59:59.9999999+00:00", "task9_completion_status": "False", "task9_daily_search_count": "0", "task9_entries_max_num": "5", "task9_progress_status": "0", "task9_puzzle_count": "0", "task9_streak_count": "0", "title": "", "total_sweepstakes_entries": "1", "total_sweepstakes_entries_max_num": "340", "type": "highvaluesweepstakes", "week_10_winners_image_url": "", "week_1_winners_image_url": "https://rewards.bing.com/rewardscdn/images/rewards/1Mhva/winners_2M_week1_V1.png", "week_2_winners_image_url": "https://rewards.bing.com/rewardscdn/images/rewards/1Mhva/winners_2M_week2_V1.png", "week_3_winners_image_url": "", "week_4_winners_image_url": "", "week_5_winners_image_url": "", "week_6_winners_image_url": "", "week_7_winners_image_url": "", "week_8_winners_image_url": "", "week_9_winners_image_url": ""}, "name": "HVA_1M_Sweeps_info", "priority": 1, "tags": ["exclude_global_config"]}], "promotions": [{"attributes": {"State": "<PERSON><PERSON><PERSON>", "activityprogress": "0", "animated_icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_small.png", "complete": "False", "description": "Search here for 3 days and earn an extra 3,100 points.", "destination": "", "edgebar_description": "", "edgebar_disclaimer": "Offer valid for 1 person/account within 7 days of joining the challenge", "edgebar_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Microsoft_giftcard_grey.png", "edgebar_link_text": "Get started", "edgebar_title": "Welcome to search bar powered by Microsoft Edge! Get a free gift card when you search here for 3 days.", "edgebar_type": "eligible", "give_eligible": "False", "icon": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Icons/newEdgeLogo.json", "image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/278x180/Star-magenta-278x180px.png", "link_text": "Get started", "max": "0", "offerid": "eligibility_EdgeBarMicrosoft_202211_ML293H", "progress": "0", "promotional": "0", "sc_bg_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_medium.png", "sc_bg_large_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Animated-Banners/Banner_Rewards_Intro_Banner_ChromeExtension_large.png", "small_image": "https://rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Mobile/newEdgeLogo_75x75.png", "title": "Earn bonus Microsoft Rewards points", "type": "url<PERSON><PERSON>"}, "name": "ENPK_eligibility_EdgeBarMicrosoft_202211_ML293H_info", "priority": -1, "tags": ["exclude_give_pcparent", "non_global_config"]}], "refresh_status_muted_until": "13401814570306428"}, "edge_share": {"enhanced_copy_paste": {"default_url_format": 3, "enable_secondary_ecp": true}}, "edge_ux_config": {"assignmentcontext": "Y+r4K2h+HKri2jdCLXFa+/6F9WMZIqf9xrw3Pd63ADs=", "dataversion": "254520582", "experimentvariables": {"shopppdismisstreatment": {"edgeServerUX.shopping.msEdgeShoppingCashbackDismissTimeout2s": true}}, "flights": {"shopppdismisstreatment": "31004791"}, "latestcorrelationid": "Ref A: 416436A50319412B840E05455684570C Ref B: DXB251051108060 Ref C: 2025-09-01T14:16:04Z"}, "edge_vpn": {"available": true}, "edge_wallet": {"ec_cool_down_time": "13399246019363793", "ec_dismiss_count": 10, "home": {"fre": {"passwords_step_completed": true, "passwords_step_completion_state": 1}}, "notification": {"featurePromotion": {"promotionDict": {}}, "upcomingHotelReservation": {"nextRefreshTime": "2025-09-22T15:19:19.735Z", "refreshIntervalTime": 32.0}}, "passwords": {"latest_password_management_count": {"2025-08-26": 6, "2025-09-01": 1}, "latest_password_usage_count": {"2025-08-26": 6}, "password_lost_report_date": "13401199230558054"}, "trigger_funnel": {"records": []}, "wallet_checkout_user": true}, "enhanced_tracking_prevention": {"enabled": true, "user_pref": 1}, "enterprise_profile_guid": "eac6c302-5f77-41d8-9060-5793038c9739", "extension": {"installed_extension_count": 9}, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "139.0.3405.125", "pdf_upsell_triggered": false, "pinned_extension_migration": true, "pinned_extensions": [], "toolbar": ["okhjkpgblgdjappgfgakbcecdblgffcl", "llbjbkhnmlidjebalopleeepgdfgcpec", "iikmkjmpaadaobahmlepeloendndfphd", "odfafepnkmbhccpbejgmiehpchacaeak", "amnbcmdbanbkjhnfoeceemmmdiepnbpp", "abfimpkhacgimamjbiegeoponlepcbob", "apadglapdamclpaedknbefnbcajfebgh"], "ui": {"allow_chrome_webstore": true}}, "family_safety": {"activity_reporting_enabled": false, "web_filtering_enabled": false}, "fsd": {"retention_policy_last_version": 139}, "gaia_cookie": {"periodic_report_time_2": "13401209770306335"}, "google": {"services": {"consented_to_sync": true, "signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-09-01T14:16:10.598Z", "value": "17"}}}}, "import_items_failure_state": {"reimport": {"ie_react": 62432}}, "instrumentation": {"bookmark_bar": {"show_on_all_tabs": "chrome::ToggleBookmarkBarWhenVisible;false", "show_only_on_ntp": "chrome::ToggleBookmarkBarWhenVisible;false"}, "ntp": {"layout_mode": "updateToCustomLayoutMode;3;1747453714200", "news_feed_display": "updateToCustomLayoutMode;headingsonly;1747453714200"}, "show_home_button": "extensions::PrefsUtil::SetPref;true"}, "intl": {"accept_languages": "en-US,en", "selected_languages": "en-US,en"}, "local_browser_data_share": {"index_last_cleaned_time": "13401209830580856", "pin_recommendations_eligible": false}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "BEFus8OPwDiXrHc56CQq7GXtNgccOhhlqYAnH6pH4SdwiT39+UNoX2IQDthOC8wHXBaoW9LQ4fKdLJrXec4hKQ=="}, "muid": {"last_sync": "13401209770493284", "values_seen": ["06AFAE75202E62AB2FF1B82F2127639E"]}, "new_device_fre": {"imported_google_data": "8", "imported_google_data_timestamp": "13394543477427697"}, "ntp": {"background_image_type": "imageAndVideo", "hide_default_top_sites": true, "layout_mode": 3, "news_feed_display": "headingsonly", "next_site_suggestions_available": false, "num_personal_suggestions": 9, "quick_links_options": 1, "record_user_choices": [{"setting": "quick_links_options", "source": "ntp", "timestamp": 1702865531211.0, "value": "onerow"}, {"setting": "layout_mode", "source": "ntp", "timestamp": 1747453714200.0, "value": 3}, {"setting": "current_location_enabled", "source": "ntp", "timestamp": 1680147130202.0, "value": false}, {"setting": "daily_discovery", "source": "ntp", "timestamp": 1680147130202.0, "value": true}, {"setting": "ntp_tips", "source": "ntp", "timestamp": 1680147158511.0, "value": true}, {"setting": "single_column", "source": "ntp", "timestamp": 1680147212320.0, "value": "multi"}, {"setting": "tscollapsed", "source": "tscollapsed_to_off", "timestamp": 1702865531211.0, "value": 0}, {"setting": "breaking_news_dismissed", "source": "ntp", "timestamp": 1749851151463.0, "value": {}}, {"setting": "ntp.news_feed_display", "source": "ntp", "timestamp": 1747437755222.0, "value": "headingsonly"}, {"setting": "is_ruby_page", "source": "ntp", "timestamp": 1756730159718.0, "value": "0"}, {"setting": "ruby_cookie_change_history", "source": "ntp", "timestamp": 1756061084290.0, "value": "1|1756061084290|0"}, {"setting": "ruby_ux_history", "source": "ntp", "timestamp": 1756227850521.0, "value": "false"}, {"setting": "ntp.is_ruby", "source": "ntp", "timestamp": 1756227850521.0, "value": "false"}], "show_greeting": true}, "nurturing": {"time_of_last_sync_consent_view": "13401209770613750"}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SAVED_TAB_GROUP": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}}, "password_manager": {"autofillable_credentials_profile_store_login_database": true, "profile_store_migrated_to_os_crypt_async": true}, "personalization_data_consent": {"how_set": 15, "personalization_in_context_consent_can_prompt": false, "personalization_in_context_count": 0, "personalization_in_context_has_prompted": false, "when_set": "13331598402659208"}, "privacy_sandbox": {"first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 20, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {"https://drive.google.com:443,*": {"last_modified": "13392879374739203", "setting": 1}, "https://mail.google.com:443,*": {"last_modified": "13395193193787149", "setting": 1}, "https://techverx-my.sharepoint.com:443,*": {"last_modified": "13374084725018641", "setting": 1}}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "clear_browsing_data_cookies_exceptions": {}, "client_hints": {"https://music.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {"*,https://accounts.google.com:443": {"last_modified": "*****************", "setting": 1}, "*,https://www.facebook.com:443": {"last_modified": "*****************", "setting": 1}, "https://[*.]accounts.google.com:443,*": {"last_modified": "*****************", "setting": 1}, "https://[*.]check-iban.com:443,*": {"last_modified": "*****************", "setting": 1}, "https://[*.]lookmovie.ag:443,*": {"last_modified": "*****************", "setting": 1}, "https://[*.]stackoverflow.com:443,*": {"last_modified": "*****************", "setting": 1}, "https://[*.]www.udemy.com:443,*": {"last_modified": "*****************", "setting": 1}, "https://[*.]www.youtube.com:443,*": {"last_modified": "*****************", "setting": 1}}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "edge_ad_targeting": {}, "edge_ad_targeting_data": {}, "edge_sdsm": {}, "edge_split_screen": {}, "edge_u2f_api_request": {}, "edge_user_agent_token": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:61761,*": {"expiration": "13408985954373309", "last_modified": "13401209954373315", "lifetime": "7776000000000", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {"http://www.lenovo.com:80,*": {"last_modified": "13358445445469983", "setting": 1}}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "secure_network": {}, "secure_network_sites": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:61761,*": {"last_modified": "13401209821347190", "setting": {"lastEngagementTime": 1.3401209821347176e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 4.8, "rawScore": 4.8}}}, "sleeping_tabs": {}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "tech_scam_detection": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "trackers": {}, "trackers_data": {}, "tracking_org_exceptions": {}, "tracking_org_relationships": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "139.0.3405.125", "creation_time": "13401209770277828", "default_content_setting_values": {"popups": 1}, "edge_crash_exit_count": 0, "edge_password_is_using_new_login_db_path": false, "edge_password_login_db_path_flip_flop_count": 0, "edge_passwords_more_menu_label_shown": true, "edge_profile_id": "7f056b53-0329-4a96-83cb-065358fb1d37", "edge_user_with_non_zero_passwords": true, "exit_type": "Normal", "hard_yes_password_monitor_consent": true, "has_seen_signin_fre": false, "is_relative_to_aad": true, "isolated_web_app": {"install": {"pending_initialization_count": 0}}, "last_engagement_time": "13401209821347176", "last_time_obsolete_http_credentials_removed": 1756736230.317731, "last_time_password_monitor_consent_shown": "13324462358262800", "last_time_password_monitor_ooc_alert_shown": "13394498185184139", "last_time_password_store_metrics_reported": 1756736200.322651, "managed_user_id": "", "name": "Profile 1", "network_pbs": {"e6a0fe71": {"last_updated": "13401201908874858", "pb": 13}}, "number_password_monitor_consent_shown": 1, "observed_session_time": {"feedback_rating_in_product_help_observed_session_time_key_139.0.3405.125": 48.0}, "password_breach_last_scanned_error": 0, "password_breach_last_scanned_time": "13401209781287163", "password_breach_scan_trigger_last_reset_time": "13400704437714778", "password_breach_scan_triggered_count": 0, "password_breach_scan_triggered_password_count": 0, "password_breach_scanned": true, "password_hash_data_list": [], "signin_fre_seen_time": "13401209770298846", "using_default_avatar": false, "using_gaia_avatar": true, "were_old_google_logins_removed": true}, "read_aloud": {"last_used_time": "13388797309176317", "speech_preferences": "{\"languageSettings\":[{\"language\":\"en\",\"voiceURI\":\"Microsoft Emma Online (Natural) - English (United States)\",\"rate\":1.25,\"version\":1}]}"}, "reset_prepopulated_engines": false, "safebrowsing": {"advanced_protection_last_refresh": "13401209772167889"}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "sessions": {"event_log": [{"crashed": false, "time": "*****************", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "*****************", "type": 2, "window_count": 1}], "session_data_status": 5}, "shopping": {"contextual_features_enabled": true, "dma_telemetry_expiration_time": "*****************", "pcb_supported": true}, "should_read_incoming_syncing_theme_prefs": false, "signin": {"accounts_metadata_dict": {"0003BFFD0C579F7E": {"BookmarksExplicitBrowserSigninEnabled": false, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "smart_explore": {"on_image_hover": false}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "surf_game": {"buoy_highscore": -1, "classic_highscore": 2000, "speed_highscore": -1}, "sync": {"apps": true, "autofill": true, "bookmarks": true, "cached_passphrase_type": 2, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "collections": true, "collections_edge_re_evaluated": true, "collections_edge_supported": true, "edge_account_type": 1, "edge_wallet": true, "edge_wallet_edge_supported": true, "encryption_bootstrap_token_per_account_migration_done": true, "extensions": true, "extensions_edge_supported": true, "first_full_sync_completed": true, "gaia_id": "0003BFFD0C579F7E", "has_been_enabled": true, "has_setup_completed": true, "history_edge_supported": true, "keep_everything_synced": true, "keystore_encryption_key_state": "****************************************************************************************************************************************************************************************************************************************************************************************", "local_device_guids_with_timestamp": [{"cache_guid": "Nadv6otwPTwRdAdZhQTMJw==", "timestamp": 155106}], "passwords": true, "passwords_per_account_pref_migration_done": true, "preferences": true, "tabs": true, "tabs_edge_supported": true, "transport_data_per_account": {"QzD21naZm4z5N07MvbqGJrmXpS3RJ80hCF4Ir02sADM=": {"sync.bag_of_chips": "", "sync.birthday": "ProductionEnvironmentDefinition", "sync.cache_guid": "Nadv6otwPTwRdAdZhQTMJw==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "***********"}}, "typed_urls": true}, "sync_consent_recorded": true, "sync_profile_info": {"edge_ci_consent_last_modified_date": "*****************", "edge_ci_consent_last_shown_date": "*****************", "edge_ci_is_option_explicitly_selectedby_user": false, "edge_san_consent_last_modified_date": "*****************", "edge_san_consent_last_shown_date": "*****************", "edge_san_is_option_explicitly_selectedby_user": false}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_groups": [], "tab_groups_cleanup_version": 4, "tab_groups_migration_version": 3, "third_party_search": {"consented": false}, "toolbar": {"pinned_chrome_labs_migration_complete": true}, "toolbar_declutter": {"new_user_cleanup_triggered": true, "undo": {"last_time": "*****************"}}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 270, "tracking_prevention": {"strict_inprivate": false}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "user_experience_metrics": {"personalization_data_consent_enabled": true, "personalization_data_consent_enabled_last_known_value": true, "reporting_personalization_enabled": true}, "video_enhancement": {"mode": "Non-AI enhancement"}, "visual_search": {"dma_state": 1}, "web_app_install_metrics": {"cinhimbnkkaeohfgghhklpknlkffjgod": {"install_source": 16, "install_timestamp": "*****************"}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "139", "link_handling_info": {"enabled_for_installed_apps": true}}}