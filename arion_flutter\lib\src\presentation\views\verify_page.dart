import 'package:arion_flutter/src/data/repositories/auth_repository.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class VerifyPage extends StatefulWidget {
  final String? token;
  const VerifyPage({super.key, this.token});

  @override
  State<VerifyPage> createState() => _VerifyPageState();
}

class _VerifyPageState extends State<VerifyPage> {
  bool _loading = true;
  String? _error;
  Map<String, dynamic>? _data;

  @override
  void initState() {
    super.initState();
    _start();
  }

  Future<void> _start() async {
    final token = widget.token;
    if (token == null || token.isEmpty) {
      setState(() { _loading = false; _error = 'Missing token'; });
      return;
    }
    try {
      final repo = GetIt.I<AuthRepository>();
      final res = await repo.verifyEmail(token);
      if (res['success'] == true) {
        setState(() { _data = res; _loading = false; });
      } else {
        setState(() { _error = (res['error'] ?? 'Verification failed').toString(); _loading = false; });
      }
    } catch (e) {
      setState(() { _error = 'Network error'; _loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Verify Email')),
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 520),
          child: Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _loading ? _buildLoading() : (_error != null ? _buildError() : _buildSuccess()),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() {
    return Column(mainAxisSize: MainAxisSize.min, children: const [
      SizedBox(height: 16),
      CircularProgressIndicator(),
      SizedBox(height: 16),
      Text('Verifying your email...'),
    ]);
  }

  Widget _buildError() {
    return Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.center, children: [
      const Icon(Icons.error_outline, color: Colors.red, size: 48),
      const SizedBox(height: 12),
      Text(_error!, style: const TextStyle(color: Colors.red)),
      const SizedBox(height: 16),
      ElevatedButton(onPressed: _start, child: const Text('Try Again')),
    ]);
  }

  Widget _buildSuccess() {
    final email = _data?['userEmail'] ?? '';
    final sessionId = _data?['sessionId'];
    return Column(mainAxisSize: MainAxisSize.min, children: [
      const Icon(Icons.check_circle_outline, color: Colors.green, size: 48),
      const SizedBox(height: 12),
      const Text('Email Verified Successfully!', style: TextStyle(fontWeight: FontWeight.w600)),
      const SizedBox(height: 8),
      if (email is String && email.isNotEmpty) Text('Email: $email'),
      const SizedBox(height: 16),
      ElevatedButton(
        onPressed: () {
          Navigator.of(context).pushReplacementNamed('/demo', arguments: {'sessionId': sessionId});
        },
        child: const Text('Access Demo Platform'),
      ),
      const SizedBox(height: 8),
      TextButton(onPressed: () => Navigator.of(context).pushReplacementNamed('/'), child: const Text('Return to Home')),
    ]);
  }
}


