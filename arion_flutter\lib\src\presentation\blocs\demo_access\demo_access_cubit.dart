import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../data/repositories/demo_access_repository.dart';

part 'demo_access_state.dart';

class DemoAccessCubit extends Cubit<DemoAccessState> {
  final DemoAccessRepository _repo;
  DemoAccessCubit(this._repo) : super(const DemoAccessState.initial());

  Future<void> submit(Map<String, dynamic> data) async {
    emit(state.copyWith(status: DemoAccessStatus.loading));
    try {
      final res = await _repo.requestAccess(data);
      if (res['success'] == true) {
        emit(state.copyWith(status: DemoAccessStatus.success));
      } else {
        emit(state.copyWith(status: DemoAccessStatus.failure, error: res['error']?.toString() ?? 'Submission failed'));
      }
    } catch (e) {
      emit(state.copyWith(status: DemoAccessStatus.failure, error: e.toString()));
    }
  }
}


