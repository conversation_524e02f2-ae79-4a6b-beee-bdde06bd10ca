import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';

class AdminMfaDialog extends StatefulWidget {
  const AdminMfaDialog({super.key});

  @override
  State<AdminMfaDialog> createState() => _AdminMfaDialogState();
}

class _AdminMfaDialogState extends State<AdminMfaDialog> {
  final _pwd = TextEditingController();
  final _code = TextEditingController();
  bool _sent = false;
  bool _busy = false;
  String? _error;

  Future<void> _requestCode() async {
    setState(() { _busy = true; _error = null; });
    try {
      final api = GetIt.I<ApiClient>();
      final cfg = GetIt.I<ConfigService>();
      await cfg.load();
      final action = cfg.actions['adminRequestCode'] ?? 'admin-request-code';
      final res = await api.post(action, { 'password': _pwd.text.trim() });
      final data = res.data as Map? ?? {};
      if (data['success'] == true) {
        setState(() => _sent = true);
      } else {
        setState(() => _error = (data['error'] ?? 'Failed to send code').toString());
      }
    } catch (e) {
      setState(() => _error = 'Network error');
    } finally {
      setState(() => _busy = false);
    }
  }

  Future<void> _verifyCode() async {
    setState(() { _busy = true; _error = null; });
    try {
      final api = GetIt.I<ApiClient>();
      final cfg = GetIt.I<ConfigService>();
      await cfg.load();
      final action = cfg.actions['adminVerifyCode'] ?? 'admin-verify-code';
      final res = await api.post(action, { 'code': _code.text.trim() });
      final data = res.data as Map? ?? {};
      if (data['success'] == true) {
        Navigator.of(context).pop(data['data']?['token']);
      } else {
        setState(() => _error = (data['error'] ?? 'Invalid code').toString());
      }
    } catch (e) {
      setState(() => _error = 'Network error');
    } finally {
      setState(() => _busy = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          const Text('Admin MFA Verification', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700)),
          const SizedBox(height: 12),
          if (!_sent) ...[
            TextField(controller: _pwd, obscureText: true, decoration: const InputDecoration(labelText: 'Admin Password')),
            const SizedBox(height: 12),
            ElevatedButton(onPressed: _busy ? null : _requestCode, child: Text(_busy ? 'Sending...' : 'Send Code')),
          ] else ...[
            TextField(controller: _code, decoration: const InputDecoration(labelText: 'Verification Code')),
            const SizedBox(height: 12),
            ElevatedButton(onPressed: _busy ? null : _verifyCode, child: Text(_busy ? 'Verifying...' : 'Verify')),
          ],
          if (_error != null) Padding(padding: const EdgeInsets.only(top: 8), child: Text(_error!, style: const TextStyle(color: Colors.red))),
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancel')),
        ]),
      ),
    );
  }
}


