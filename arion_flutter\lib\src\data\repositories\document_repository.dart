import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../core/services/session_service.dart';

class DocumentRepository {
  final ApiClient _api;
  final ConfigService _config;
  final SessionService _session;

  DocumentRepository(this._api, this._config, this._session);

  Future<Map<String, dynamic>> getDocuments() async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['getDocuments'] ?? 'get-documents';
    final res = await _api.post(action, {}, sessionId: sessionId);
    return Map<String, dynamic>.from(res.data ?? {});
  }

  Future<Map<String, dynamic>> getDocument(String documentId) async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['getDocument'] ?? 'get-document';
    final res = await _api.post(action, {'documentId': documentId}, sessionId: sessionId);
    return Map<String, dynamic>.from(res.data ?? {});
  }

  Future<String> _ensureSession() async {
    var sessionId = await _session.getSessionId();
    if (sessionId == null) {
      sessionId = _session.generateUuid();
      await _session.saveSessionId(sessionId);
    }
    return sessionId;
  }
}


