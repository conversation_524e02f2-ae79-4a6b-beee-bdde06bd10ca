{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801771822308", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://prod.rewardsplatform.microsoft.com", "supports_spdy": true}, {"anonymization": ["HAAAABgAAABodHRwczovL21pY3Jvc29mdGFwcC5uZXQ=", false, 0], "server": "https://static.edge.microsoftapp.net", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL21zbi5jb20A", false, 0], "server": "https://arc.msn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401303372031831", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "network_stats": {"srtt": 102676}, "server": "https://copilot.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801813712515", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801814839045", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801815057178", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://lh3.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801815925354", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://static.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801787832941", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL29mZmljZS5jb20AAA==", false, 0], "network_stats": {"srtt": 177619}, "server": "https://substrate.office.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801813692058", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 129887}, "server": "https://clients2.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801814233983", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 100097}, "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801814923213", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 105379}, "server": "https://googleads.g.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801815226473", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 112210}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801815452947", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", true, 0], "network_stats": {"srtt": 117858}, "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801817620807", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 198811}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801818452964", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 171857}, "server": "https://music.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401296228087776", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 101511}, "server": "https://dxncozbhwppvwpugoqjk.supabase.co"}, {"anonymization": ["GAAAABQAAABodHRwczovL2JpbmdhcGlzLmNvbQ==", false, 0], "server": "https://www.bingapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801880117530", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "network_stats": {"srtt": 261624}, "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401303421023066", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2JpbmcuY29t", false, 0], "network_stats": {"srtt": 1309974}, "server": "https://www.bing.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13403801793104532", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 168283}, "server": "https://fonts.gstatic.com"}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}, {"anonymization": ["LAAAACYAAABodHRwczovL2VkZ2Vhc3NldHNlcnZpY2UuYXp1cmVlZGdlLm5ldAAA", false, 0], "server": "https://edgeassetservice.azureedge.net", "supports_spdy": true}], "supports_quic": {"address": "*************", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}