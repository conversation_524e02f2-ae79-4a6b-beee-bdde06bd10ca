import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../data/repositories/auth_repository.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepository _repo;
  AuthCubit(this._repo) : super(const AuthState.initial());

  Future<void> signIn(String email, String password) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      final res = await _repo.authenticate(email, password);
      if (res['success'] == true && res['authenticated'] == true) {
        emit(state.copyWith(status: AuthStatus.authenticated, isAdmin: res['userType'] == 'admin', data: res));
      } else {
        emit(state.copyWith(status: AuthStatus.failure, error: 'Invalid credentials'));
      }
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.failure, error: e.toString()));
    }
  }
}


