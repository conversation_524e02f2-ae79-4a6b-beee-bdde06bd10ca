import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../core/services/session_service.dart';

class DemoAccessRepository {
  final ApiClient _api;
  final ConfigService _config;
  final SessionService _session;

  DemoAccessRepository(this._api, this._config, this._session);

  Future<Map<String, dynamic>> requestAccess(Map<String, dynamic> data) async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['requestDemoAccess'] ?? 'request-demo-access';
    final res = await _api.post(action, data, sessionId: sessionId);
    return Map<String, dynamic>.from(res.data ?? {});
  }

  Future<String> _ensureSession() async {
    var sessionId = await _session.getSessionId();
    if (sessionId == null) {
      sessionId = _session.generateUuid();
      await _session.saveSessionId(sessionId);
    }
    return sessionId;
  }
}


