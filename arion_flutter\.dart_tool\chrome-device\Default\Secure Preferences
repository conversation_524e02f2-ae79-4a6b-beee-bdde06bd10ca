{"browser": {"show_home_button": true}, "edge": {"services": {"account_id": "0003BFFD0C579F7E", "last_username": "<EMAIL>"}}, "edge_fundamentals_appdefaults": {"enclave_version": 101}, "ess_kv_states": {"restore_on_startup": {"closed_notification": false, "decrypt_success": true, "key": "restore_on_startup", "notification_popup_count": 0}, "startup_urls": {"closed_notification": false, "decrypt_success": true, "key": "startup_urls", "notification_popup_count": 0}, "template_url_data": {"closed_notification": false, "decrypt_success": true, "key": "template_url_data", "notification_popup_count": 0}}, "extensions": {"settings": {"abfimpkhacgimamjbiegeoponlepcbob": {"disable_reasons": [1], "incognito": true}, "ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "apadglapdamclpaedknbefnbcajfebgh": {"incognito": true}, "bfnaelmomeimhlpmgjnjophhpkkoljpa": {"disable_reasons": [1]}, "bhlhnicpbhignbdhedgjhgdocnmhomnp": {"disable_reasons": [1]}, "cjmfiochlaffhnellbhffgnjocahinnh": {"disable_reasons": [1]}, "cjneempfhkonkkbcmnfdibgobmhbagaj": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "cnlefmmeadmemmdciolhbnfeacpdfbkd": {"lastpingday": "*****************"}, "dcaajljecejllikfgbhjdgeognacjkkp": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "eanggfilgoajaocelnaflolkadkeghjp": {"disable_reasons": [1]}, "eckaechjaiiiffijigiigbhbfhelljmi": {"lastpingday": "*****************"}, "ehlmnljdoejdahfjdfobmpfancoibmig": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ejbalbakoplchlghecdalmeeeajnimhm": {"disable_reasons": [1], "lastpingday": "*****************"}, "ejefaeioamebhekmfaclajddbpnnobje": {"lastpingday": "*****************"}, "ejkiikneibegknkgimmihdpcbcedgmpo": {"disable_reasons": [1]}, "ekoomohieogfomodjdjjfdammloodeih": {"disable_reasons": [1]}, "fikbjbembnmfhppjfnmfkahdhfohhjmg": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.microsoftstream.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsAmDrYmQaYQlLxSAn/jTQTGNt1IffJGIJeKucE/B42d8QIyFD2RCarmHP1bmbY1YuTng2dL3J//qyvUNwXPt9cmxH9WKwi512tzOa5r2zYaCuOgP2vAIrah/bKnpO3XmUfFWj+LRcbZahOmMDMQxzPKxFKuIz2eOiakBXDE6Ok7azHJ13LLQTte1JgZIPmyFrAciPABLp/IKLfsfnebVW1YgaOyxBNyp/7bhSmoyZI3kBv8InKOpGE8pttrBg6l5zkvD67a7ViNAYkqZIpJJV5ZTQtVWCWSG0xU2y+3zXZtx8KbGbDiWUAcwNYDVPpsV+IQXVpgAplHvrZme+hAl6QIDAQAB", "manifest_version": 2, "name": "Media Internals Services Extension", "permissions": ["mediaInternalsPrivate"], "version": "2.0.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\media_internals_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fjngpfnaikknjdhkckmncgicobbkcnle": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbihlnbpmfkodghomcinpblknjhneknc": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gbmoeijgfngecijpcnbooedokgafmmji": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gcinnojdebelpnodghnoicmcdmamjoch": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "gecfnmoodchdkebjjffmdcmeghkflpib": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "hfmgbegjielnmfghmoohgmplnpeehike": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "ifoakfbpdcdoeenechcleahebpibofpc": {"disable_reasons": [1], "lastpingday": "*****************"}, "igbncjcgfkfnfgbaieiimpfkobabmkce": {"disable_reasons": [1], "lastpingday": "*****************"}, "iglcjdemknebjbklcgkfaebgojjphkec": {"account_extension_type": 0, "active_permissions": {"api": ["identity", "management", "metricsPrivate", "webstorePrivate", "hubPrivate"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "w", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://microsoftedge.microsoft.com"}, "urls": ["https://microsoftedge.microsoft.com"]}, "description": "Discover extensions for Microsoft Edge.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtMvN4+y6cd3el/A/NT5eUnrz1WiD1WJRaJfMBvaMtJHIuFGEmYdYL/YuE74J19+pwhjOHeFZ3XUSMTdOa5moOaXXvdMr5wWaaN2frHewtAnNDO64NGbbZvdsfGm/kRkHKVGNV6dacZsAkylcz5CkwTmq97wOZ7ETaShHvhZEGwRQIt4K1poxurOkDYQw9ERZNf3fgYJ9ZTrLZMAFDLJY+uSF03pClWrr8VGc8LUQ4Naktb8QSgVUlrS14AdF/ESdbhnTvvdB0e7peNWRyoNtCqLJsbtTtBL6sOnqfusnwPowuueOFI+XskOT9TvLo6PcgxhLX5+d0mM+Jtn6PFTU8QIDAQAB", "name": "Microsoft Store", "permissions": ["webstorePrivate", "management", "metricsPrivate", "identity", "hubPrivate"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\microsoft_web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ihmafllikibpmigkcoadcmckbfhibefp": {"account_extension_type": 0, "active_permissions": {"api": ["debugger", "feedbackPrivate", "fileSystem", "fileSystem.write", "app.window.fullscreen", "metricsPrivate", "storage", "tabs", "fileSystem.readFullPath", "edgeInternetConnectivityPrivate"], "explicit_host": ["edge://resources/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["edgeFeedbackPrivate.onFeedbackRequested"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"background": {"scripts": ["js/event_handler.js"]}, "content_security_policy": "default-src 'none'; script-src 'self' blob: filesystem: chrome://resources; style-src 'unsafe-inline' blob: chrome: file: filesystem: data: *; img-src * blob: chrome: file: filesystem: data:; media-src 'self' blob: filesystem:; connect-src data:"}, "description": "User feedback extension", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon128.png", "16": "images/icon16.png", "192": "images/icon192.png", "32": "images/icon32.png", "48": "images/icon48.png"}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl3vxWwvLjcMIFK4OfG6C8PmJkMhFYDKRnx+SqG23YlMG1A+bOkiNmAN1TWpFPPp1f2PpbiZGNq1y29u/QfkD+PC4bnO7GbNw/2X5tGoP0n2K+KGGAxhnr0ki/oyo2eiFGSTOXlQvTRo5q1vB+Lbg+9TbFsWKlHZyAkeZ/YGz/iijHTqw8Q4RWdl5Tp8SlUhS/92EsWhveNJLW22veaT/Up2iSeSSwfyoHVYy8LUPaD4fbyLvPQacVLJq1dac2bNDqjaNvSPgPWCnkZtDmawZrgxT53otLCES/e96xfAf8I24VHIc1pVP8LqdqKr1AV1Yxn93h3VJ2QejtEhIAWHU6QIDAQAB", "manifest_version": 2, "name": "<PERSON>", "permissions": ["chrome://resources/", "debugger", "edgeInternetConnectivityPrivate", "feedbackPrivate", {"fileSystem": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "write"]}, "fullscreen", "metricsPrivate", "storage", "windows"], "version": "*******"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\edge_feedback", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": false, "was_installed_by_oem": false}, "iikmkjmpaadaobahmlepeloendndfphd": {"incognito": true, "lastpingday": "*****************"}, "ilehaonighjijnmpnagapkhpcdbhclfg": {"disable_reasons": [1]}, "jbleckejnaboogigodiafflhkajdmpcl": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "jdiccldimpdaibmpdkjnbmckianbfold": {"account_extension_type": 0, "active_permissions": {"api": ["activeTab", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "errorReporting"], "explicit_host": ["https://*.bing.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["lifetimeHelper.js", "telemetryHelper.js", "errorHelper.js", "voiceList/voiceListRequester.js", "voiceList/voiceListSingleton.js", "voiceList/voiceModel.js", "manifestHelper.js", "config.js", "ssml.js", "uuid.js", "wordBoundary.js", "audioStreamer.js", "wordBoundaryEventManager.js", "audioViewModel.js", "background.js"]}, "description": "Provides access to Microsoft's online text-to-speech voices", "key": "AAAAB3NzaC1yc2EAAAADAQABAAAAgQDjGOAV6/3fmEtQmFqlmqm5cZ+jlNhd6XikwMDp0I7BKh+AjG3aBIG/qqwlsF/7LAGatnSxBwUwZC0qMnGXtcOPVl26Q8OvMx0gt5Va5gxca+ae0Skluj9WN9TNxPFVhw21WbCt4D9q3kb+XXDlx/7v1ktYus4Fwr/skkjADG9cIQ==", "manifest_version": 2, "name": "Microsoft Voices", "permissions": ["activeTab", "errorReporting", "metricsPrivate", "storage", "systemPrivate", "ttsEngine", "https://*.bing.com/"], "tts_engine": {"voices": [{"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, AriaNeural)", "voice_name": "Microsoft Aria Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-US", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-US, GuyNeural)", "voice_name": "Microsoft Guy Online (Natural) - English (United States)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh<PERSON><PERSON><PERSON>, XiaoxiaoNeural)", "voice_name": "Microsoft Xiaoxiao Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-CN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-CN, YunyangNeural)", "voice_name": "Microsoft Yunyang Online (Natural) - Chinese (Mainland)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-TW", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-TW, HanHanRUS)", "voice_name": "Microsoft HanHan Online - Chinese (Taiwan)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "zh-HK", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (zh-HK, TracyRUS)", "voice_name": "Microsoft Tracy Online - Chinese (Hong Kong)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ja-<PERSON>", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (<PERSON><PERSON><PERSON><PERSON>, NanamiNeural)", "voice_name": "Microsoft Nanami Online (Natural) - Japanese (Japan)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-GB", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-GB, LibbyNeural)", "voice_name": "Microsoft Libby Online (Natural) - English (United Kingdom)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pt-BR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pt-BR, FranciscaNeural)", "voice_name": "Microsoft Francisca Online (Natural) - Portuguese (Brazil)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-MX", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-MX, DaliaNeural)", "voice_name": "Microsoft Dalia Online (Natural) - Spanish (Mexico)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-IN", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-IN, PriyaRUS)", "voice_name": "Microsoft Priya Online - English (India)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-CA, HeatherRUS)", "voice_name": "Microsoft Heather Online - English (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-CA", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-CA, SylvieNeural)", "voice_name": "Microsoft Sylvie Online (Natural) - French (Canada)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "fr-FR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (fr-<PERSON><PERSON>, <PERSON>)", "voice_name": "Microsoft Denise Online (Natural) - French (France)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "de-DE", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (de-DE, KatjaNeural)", "voice_name": "Microsoft Katja Online (Natural) - German (Germany)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ru-RU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ru-RU, EkaterinaRUS)", "voice_name": "Microsoft Ekaterina Online - Russian (Russia)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "en-AU", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (en-AU, HayleyRUS)", "voice_name": "Microsoft Hayley Online - English (Australia)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "it-IT", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (it-IT, ElsaNeural)", "voice_name": "Microsoft Elsa Online (Natural) - Italian (Italy)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "ko-KR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (ko-KR, SunHiNeural)", "voice_name": "Microsoft SunHi Online (Natural) - Korean (Korea)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "nl-NL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (nl-NL, HannaRUS)", "voice_name": "Microsoft Hanna Online - Dutch (Netherlands)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "es-ES", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (es-ES, ElviraNeural)", "voice_name": "Microsoft Elvira Online (Natural) - Spanish (Spain)"}, {"codec": "audio-24khz-48kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "tr-TR", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (tr-TR, EmelNeural)", "voice_name": "Microsoft Emel Online (Natural) - Turkish (Turkey)"}, {"codec": "audio-16khz-32kbitrate-mono-mp3", "event_types": ["end", "error", "start", "word"], "lang": "pl-PL", "remote": true, "server_name": "Microsoft Server Speech Text to Speech Voice (pl-PL, PaulinaRUS)", "voice_name": "Microsoft Paulina Online - Polish (Poland)"}]}, "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\microsoft_voices", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "jemjknhgpjaacbghpdhgchbgccbpkkgf": {"disable_reasons": [1], "lastpingday": "*****************"}, "jiidiaalihmmhddjgbnbgdfflelocpak": {"disable_reasons": [1]}, "kchaponcodemjigejilffhfchecpgdpf": {"disable_reasons": [1]}, "kfihiegbjaloebkmglnjnljoljgkkchm": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mnbndgmknlpdjdnjfmfcdjoegcckoikn": {"lastpingday": "*****************"}, "ncbjelpjchkpbikbpkcchkhkblodoama": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["background.js"]}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.skype.com/*", "https://*.teams.live.com/*"]}, "incognito": "split", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtAdFAR3ckd5c7G8VSzUj4Ltt/QRInUOD00StG95LweksGcLBlFlYL46cHFVgHHj1gmzcpBtgsURdcrAC3V8yiE7GY4wtpOP+9l+adUGR+cyOG0mw9fLjyH+2Il0QqktsNXzkNiE1ogW4l0h4+PJc262j0vtm4hBzMvR0QScFWcAIcAErlUiWTt4jefXCAYqubV99ed5MvVMWBxe97wOa9hYwAhbCminOepA4RRTg9eyi0TiuHpq/bNI8C5qZgKIQNBAjgiFBaIx9hiMBFlK4NHUbFdgY6Qp/hSCMNurctwz1jpsXEnT4eHg1YWXfquoH8s4swIjkFCMBF6Ejc3cUkQIDAQAB", "manifest_version": 2, "name": "WebRTC Internals Extension", "permissions": ["webrtcInternalsPrivate"], "version": "2.0.2"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\webrtc_internals", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ngpicahlgepngcpigiiebnheihgbaenh": {"disable_reasons": [1]}, "nkbndigcebkoaejohleckhekfmcecfja": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.teams.microsoft.com/*", "https://*.teams.live.com/*", "https://*.skype.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "WebRTC Extension", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "2.3.24"}, "path": "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\139.0.3405.125\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "odfafepnkmbhccpbejgmiehpchacaeak": {"incognito": true, "lastpingday": "*****************"}, "ofefcgjbeghpigppfmkologfjadafddi": {"active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "disable_reasons": [8192]}, "okhjkpgblgdjappgfgakbcecdblgffcl": {"incognito": true, "lastpingday": "*****************"}}}, "google": {"services": {"last_signed_in_username": "<EMAIL>"}}, "homepage": "https://www.perplexity.ai/", "homepage_is_newtabpage": false, "protection": {"macs": {"browser": {"show_home_button": "F314216D8989B832967968F953A0C9DDC87A6FB4E924D6DF9D06520C1E0302D2"}, "default_search_provider_data": {"template_url_data": "C95AB75FC40940475EAED49C3D6957140A3ED6F52464BEC2D74D3B3E358AF18E"}, "edge": {"services": {"account_id": "7E6BA44EE8BAD6E7B8684C9442AF41A88229D76EBF120A46FABE0A2B55A61E43", "last_username": "0B5AB4B5A1C94C649CA96ADF165079DCB977BDF014A01C663D32A213DBECC562"}}, "enterprise_signin": {"policy_recovery_token": "0466C89191A9D4C8FF39AA42142A444AD999473C78CAE9A5CF77F7C4C105C83C"}, "extensions": {"settings": {"abfimpkhacgimamjbiegeoponlepcbob": "4C0439704840533E57DB07CD91F615ADAC98BB86FD689A811B0AC387E27BF36D", "ahfgeienlihckogmohjhadlkjgocpleb": "F86D6AA3ACB46D7E32650880D16BEAB1D1E7435F2402ABDB7DCE6A49949601B5", "apadglapdamclpaedknbefnbcajfebgh": "AA309DA9FC491B0BBFA763FD5998EC5B94F8FFE4D8D9589BCB45D711FFA9E666", "bfnaelmomeimhlpmgjnjophhpkkoljpa": "EDF0DAC1FF37BD6BBA849491CB0B0AA9970D6A780BCAA7E688F5E7BAB7146D13", "bhlhnicpbhignbdhedgjhgdocnmhomnp": "03CBB54231A15B95D85C5BC7A211FF5C1D2C94815CA9A9899CD4E2C507F0A375", "cjmfiochlaffhnellbhffgnjocahinnh": "E043B1FCEAF6ACA7E91368314064BF65098F92AE8B253A8CB66A66B07F64459F", "cjneempfhkonkkbcmnfdibgobmhbagaj": "51865BFCBD71B31B07E8038CC95ECBC5645EA5E682C35902DC72B01203F9EF5C", "cnlefmmeadmemmdciolhbnfeacpdfbkd": "5929967120A1C48B90AB38CB2E2A44D76E3B327617182E9A21CF5CC673C77071", "dcaajljecejllikfgbhjdgeognacjkkp": "CF2810A51815D5E36BE660E907A7AE1B1803271274F33E4A1FDE2166F0FEACA8", "dgiklkfkllikcanfonkcabmbdfmgleag": "EC5917E77CC61FD0BAB1618FF5E5F8DD09B48C2E1A0F74E687D193C77B9A249E", "eanggfilgoajaocelnaflolkadkeghjp": "DF04CEE90B24B998167EA69F55CDF29DEFF5257D212E9ADD28B5FA3091B3FD9B", "eckaechjaiiiffijigiigbhbfhelljmi": "C983108FA2897956CA74F6C55D6AC5D324C41F0DA74DC8C0362DD7332B9E883C", "ehlmnljdoejdahfjdfobmpfancoibmig": "2CD8E8FC576FBF240212618ACB12485459CCD9313D638DF3682232FD56F15E02", "ejbalbakoplchlghecdalmeeeajnimhm": "3DCA269CC99A6A6AB4D4A69EB0DE0416B0F67D2E32CD8F90049A70424F731485", "ejefaeioamebhekmfaclajddbpnnobje": "50F74A1A7785D0D0247F1D7185ECFDA0CAA8D1A81DC58694D28DDE69DD546DB5", "ejkiikneibegknkgimmihdpcbcedgmpo": "F83BA5E99E5971BBF597FDB400A03B710D5A36203DF854B31D9D785C2D8E536F", "ekoomohieogfomodjdjjfdammloodeih": "72E2531E214831C05844EAF7564E1758EC926252DC0AAEE483FE5A1EDA13C28D", "fikbjbembnmfhppjfnmfkahdhfohhjmg": "3141074C53CCB52ED2B4F6839696FF000A7E3A00DD930376BAE26433F1940DBD", "fjngpfnaikknjdhkckmncgicobbkcnle": "2EA8FF3FE0C16FE9A67BAF3132C3C9F5FD81A9581CC05C7C550F6A1BDD609A66", "gbihlnbpmfkodghomcinpblknjhneknc": "A14C449538B8172A57621451D6945E9D66D41EF5A28756B87762BB7C2025D5A2", "gbmoeijgfngecijpcnbooedokgafmmji": "5A21911DBE67CB7A66AC762ED957CF75459577DFA12395007014E64952D565D9", "gcinnojdebelpnodghnoicmcdmamjoch": "53094CCDDCD16A96B2725D1119A1D6F4737BD3D5BDF47F4E950C313FCE77C2A4", "gecfnmoodchdkebjjffmdcmeghkflpib": "BAE2841389C9D537B1B2048095F336D0BF4606FC6B8E0A2430ED86A0338035CF", "hfmgbegjielnmfghmoohgmplnpeehike": "C0DEA36CE525AB050EA93485BB02989834FF9DBB36AA528B5FFA551412825A86", "ifoakfbpdcdoeenechcleahebpibofpc": "2E7C1AD3C231C1483E933CF9B4AC3BC3A3925ED3320602A89E0FEA1951FFFBE6", "igbncjcgfkfnfgbaieiimpfkobabmkce": "18546024B5D36696127C66CCB823B2862E466E396ED0FB490032AD422ECF7678", "iglcjdemknebjbklcgkfaebgojjphkec": "35113A9E8AB88629D3E44C329E370FE3F91696CB2ABD42E1AB79C80130251668", "ihmafllikibpmigkcoadcmckbfhibefp": "BF09CEF12A07C1ADD2B438036AED86E9BE19BDA59DBF015B24B4A9BD359045E3", "iikmkjmpaadaobahmlepeloendndfphd": "EABE4BCD25FD3DCED22EB1583981C23EE9F5A58FDE72621AEB09E8BD02E51AC7", "ilehaonighjijnmpnagapkhpcdbhclfg": "7A88643510E3B01ED506D366EAC1800060B6DE48F751F33372FB94CF9B91E614", "jbleckejnaboogigodiafflhkajdmpcl": "3BEB6B147DEF43369E3EFBF7F98FF8B0CE5C23A9A0B20D1E4F0DF8C4C2762AB8", "jdiccldimpdaibmpdkjnbmckianbfold": "7FFAE34243C73EE3DCF8D0DC587D0DEECA1681FEEE5D57CD0E4542609C27764C", "jemjknhgpjaacbghpdhgchbgccbpkkgf": "D61B67CFC93DBF9DCAC8F13470CAD1BB97D2BBF5DDA566BFE04D9B67E925FFDE", "jiidiaalihmmhddjgbnbgdfflelocpak": "221B5F2F4699EB0EAC352D3D4DF6E0C911CD5B1FDFA94369E9D0FC40D7852645", "kchaponcodemjigejilffhfchecpgdpf": "C9C7332762196638FC17DD83D83E6F119FFF12F466456FFE2FEF7BF30D6D1814", "kfihiegbjaloebkmglnjnljoljgkkchm": "EB0DD6C8747406F1134C3416FCDD0786461ABFDFB80BB59081C1B5FB562BCEB8", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "537E6F474F4A45E1F0313FA2851227F79DECA51BF47740B30BDDF8FDBCBC28DC", "mnbndgmknlpdjdnjfmfcdjoegcckoikn": "3ED156BC3EFDE3395A402CF5A73A1771950DBC8FE595142BFDCFD7D045CA5437", "ncbjelpjchkpbikbpkcchkhkblodoama": "BD178C52FE948BCD18C05C6315B067D2E673BCBC845C29BF18067AF8194CF89F", "ngpicahlgepngcpigiiebnheihgbaenh": "802A6014D7F046F5A868F809971CA2C8F16B99AF17CBE8E6F356D166E997A232", "nkbndigcebkoaejohleckhekfmcecfja": "7CC1D01731C87FEA466258F4E9ED000E835B9A9FA579A50216A3B3FB6F059FE6", "nkeimhogjdpnpccoofpliimaahmaaome": "AE5E7F088F7CBBFF9D45A7FA408AFFDC96709F0878523D11125BA287DB3B1DD1", "odfafepnkmbhccpbejgmiehpchacaeak": "F880589AB99930416C71E7F88CBBA1610FD8CFA21B54549B559902DD75C9719A", "ofefcgjbeghpigppfmkologfjadafddi": "37E3FF4F16FD06F72D663E9A531C812F5E839A08F0A54A28734610AEB0290EBF", "okhjkpgblgdjappgfgakbcecdblgffcl": "1BD545E317FA2F0D0B6E1D94F58C40B57F357EDC6B8890459A6E47FFBCEC366D"}, "ui": {"developer_mode": "E533810FB1B030C79A22F65006878CD4079DCFA64566FE3EE14A7501FA815EF3"}}, "google": {"services": {"last_signed_in_username": "0EEA141F6718723FDA808D635EDD12729CB146B3E1483504FD2946D57456F99B"}}, "homepage": "B7FE8D4473EE9FB228E5ACC3A1C665E5199ED8ACE4622A496643C058A0B6A0FD", "homepage_is_newtabpage": "DD34B1099950775FEDF66F0570B999D48F74273C6D8531F0A8557A31F24A5BCC", "media": {"cdm": {"origin_data": "E365A386E942C1108CB07CB4EA71D0C5A731B7866B4CA0BE89BA90A5AC9C7DF0"}, "storage_id_salt": "89DB5EA76A9FAD1549C5845BAC78A95BCA02E5D86FD14C73818B60A8651ABA4D"}, "pinned_tabs": "8C0D8E07EEDF22E99058537654B15C4368C33B7B9B07F3DF31EA9809F36F3DFF", "prefs": {"preference_reset_time": "3C16F44D9AC80C1DD435325D320E11FC0897CB31BD8F22386F4CEA4653771F8C"}, "safebrowsing": {"incidents_sent": "A98C6965EF4C33B6CBBEFBF0022220F646E3FE7D04E9B6F221A062C516F3A688"}, "schedule_to_flush_to_disk": "338CC25B433E638F39CD82AEFA6A12120095494410C069161BEA7550B9667784", "search_provider_overrides": "171F83D34A084797A57932187DD31DBA73B2F23DA34E127523F4128FA5A65F55", "session": {"restore_on_startup": "85BF00320159CBB6E115D4278740687581E4D67528CF85FACFEBAEC239875965", "startup_urls": "3FCA1BC53975B23C74A0E244C40FC654ADC672B55F85C372BC5B8A32881E2B33"}}, "super_mac": "95FC639D455A22A1AE95351FDF9DCA1AAD3FF5412DAC1B73D4408EB0F1EA1379"}, "session": {"restore_on_startup": 5, "restore_on_startup_edge_enclave": "E90000000100000001010000010000006D76B04CE82E399E6D299DDF5B2DD327AF8403C31E95D99303AF5332FF7E31B78811C0B9BABC5E7C10432D389604124C0300000000000000F8A051C46271405EBBB8F49F69AC02740B7BD647EC8249A09EFDC59CD22498895994F30058CF2F220DB479F3E5BAF96B6D3CD1B6E1712264E092955FB2ADCC621106B773B40572F7D02A0CFF794B00E10E1331BBA27AB0619DB8187376744A5C407C7E1F251E4312B56F1161F9E856625A46FC4386A840E5BCAAC98F066A0A0265000000000000000200000000000000000000001000000005000000AFD7782468", "restore_on_startup_edge_enclave_verify": "756b127a2dd548785075e51a64a01c1c", "startup_urls": ["https://ethermine.org/miners/d079Adc94a510829D5CC66ae9A045b10d036e782/dashboard", "https://www.binance.com/en/trade/ETH_BUSD", "https://etherscan.io/gastracker", "https://www.google.com/search?q=ethereum+price+usd&rlz=1C1GCEA_enPK951PK951&oq=&aqs=chrome.1.35i39i362l8...8.122187045j0j7&sourceid=chrome&ie=UTF-8", "https://pythontutor.com/visualize.html#mode=display", "https://www.geeksforgeeks.org/recursion-practice-problems-solutions/", "https://www.geeksforgeeks.org/recursion/", "https://www.arcai.com/netCut/s/"], "startup_urls_edge_enclave": "E9020000010000000101000001000000C97F29134B9958CDE738240576CC884B01DCF260435445F732F14045C262081800B300A2976745EF35DE3B08A0DE21DC0300000000000000F8A051C46271405EBBB8F49F69AC02740B7BD647EC8249A09EFDC59CD22498895994F30058CF2F220DB479F3E5BAF96B6D3CD1B6E1712264E092955FB2ADCC621106B773B40572F7D02A0CFF794B00E10E1331BBA27AB0619DB8187376744A5C407C7E1F251E4312B56F1161F9E856625A46FC4386A840E5BCAAC98F066A0A0265000000000000000200000000000000000000001000000005020000627D2FB73D91D7B56DDE509C12B66FE08107286CC63AC6F5A9A31C9C7E6C19B18B1B506E9A5300679683FA4211A046FB8564FC337CA5B35734401E8CC3158382A45CA8C0440444A0E509729D7CFED2587E8BF88E32AE76FF8ECC608779FB9F9011A7AC4CF037E3DE7138CC28AA3CEDBA0CD7466D902540ACE3CD715A2C14AA7E1030135AF8BC217495CCD222306606C9F33AC9DFD83F9388CAAAB309F7B78DA4C0DEE79E8BE5AA91F3BE34F30A69B489DC9391D8476E5499527075ACB01E7DAF818BF6272DB910A45F2B064CD94B4EECBB1DE0A595040996DC81C100A26C739EF5E873A07F6666C7CF0889C050F74D621482FD59358CCB28B31D1870E8D5A4768650934D2C06F9AB8FB7EF04EBAB7BA3C5C4A45AB52CDCFD8FE7F3BF01773F2E7B4144355F8B987861283FCF743D1B13DCABB7B5305F1D88EBD01A42D21E636D4B5BA45F67A9957EDFA818791D44E8821ED4391D9D178062687754651B73DF4A2C2FEC06C0CDB43A718D9A7205E4168B1EC75B04391402A71AF194C8E3D6295C4F50D17A3468BC5BCF081C1F233070DA3011513BD808CED38249694FB655F24C28BEFE51AFF617901CBC07ED651B38E0AF2589BCFAC2FE67DDDFB7AF54EE5FD0A51C57DC26B4B6C8DA799CB2C2E9F89B3F9C63FEB82B6FD1751DCF969EF2213ECDD8C5DAE0545D789B36526A287EC81730C1CBF70F74B8C26EF384610E791B32D4D4B406C4", "startup_urls_edge_enclave_verify": "67445264b3ee5992b0af22d1cd5ccf89"}}