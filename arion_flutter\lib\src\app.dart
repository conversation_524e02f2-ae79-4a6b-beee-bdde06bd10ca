import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/di/locator.dart';
import 'presentation/theme/app_theme.dart';
import 'presentation/routes/app_router.dart';
import 'presentation/blocs/auth/auth_cubit.dart';
import 'presentation/blocs/demo_access/demo_access_cubit.dart';
import 'data/repositories/auth_repository.dart';
import 'data/repositories/demo_access_repository.dart';

class ArionApp extends StatefulWidget {
  const ArionApp({super.key});

  @override
  State<ArionApp> createState() => _ArionAppState();
}

class _ArionAppState extends State<ArionApp> {
  @override
  void initState() {
    super.initState();
    setupLocator();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(create: (_) => AuthCubit(locator<AuthRepository>())),
        BlocProvider<DemoAccessCubit>(create: (_) => DemoAccessCubit(locator<DemoAccessRepository>())),
      ],
      child: MaterialApp(
        title: 'ArionComply',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        initialRoute: AppRouter.initialRoute,
        onGenerateRoute: AppRouter.onGenerateRoute,
      ),
    );
  }
}


