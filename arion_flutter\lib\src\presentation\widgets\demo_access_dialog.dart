import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../blocs/demo_access/demo_access_cubit.dart';

class DemoAccessDialog extends StatefulWidget {
  const DemoAccessDialog({super.key});

  @override
  State<DemoAccessDialog> createState() => _DemoAccessDialogState();
}

class _DemoAccessDialogState extends State<DemoAccessDialog> {
  final _formKey = GlobalKey<FormState>();
  final _firstName = TextEditingController();
  final _lastName = TextEditingController();
  final _workEmail = TextEditingController();
  final _company = TextEditingController();
  String? _jobTitle;
  String? _companySize;
  String? _interest;
  final _password = TextEditingController();
  final _confirmPassword = TextEditingController();
  bool _agreeProcessing = false;
  bool _agreePrivacy = false;
  bool _agreeMarketing = false;
  bool _submitting = false;
  bool _success = false;

  @override
  void dispose() {
    _firstName.dispose();
    _lastName.dispose();
    _workEmail.dispose();
    _company.dispose();
    _password.dispose();
    _confirmPassword.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate() || !_agreeProcessing || !_agreePrivacy) {
      setState(() {});
      return;
    }
    setState(() => _submitting = true);
    try {
      final data = {
        'firstName': _firstName.text.trim(),
        'lastName': _lastName.text.trim(),
        'workEmail': _workEmail.text.trim(),
        'company': _company.text.trim(),
        'jobTitle': _jobTitle,
        'companySize': _companySize,
        'complianceInterest': _interest,
        'agreedToDataProcessing': _agreeProcessing,
        'agreedToPrivacy': _agreePrivacy,
        'agreedToMarketing': _agreeMarketing,
      };
      final cubit = context.read<DemoAccessCubit>();
      await cubit.submit(data);
      final state = cubit.state;
      if (state.status == DemoAccessStatus.success) {
        setState(() => _success = true);
      } else if (state.status == DemoAccessStatus.failure) {
        throw Exception(state.error ?? 'Submission failed');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Failed to submit request. Please try again.')));
      }
    } finally {
      if (mounted) setState(() => _submitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 500),
        child: AnimatedCrossFade(
          crossFadeState: _success ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 250),
          firstChild: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Get Demo Access', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w700)),
                const SizedBox(height: 4),
                const Text('Register to access our AI-powered compliance platform'),
                const SizedBox(height: 12),
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      Row(children: [
                        Expanded(child: _field(_firstName, 'First Name *')),
                        const SizedBox(width: 12),
                        Expanded(child: _field(_lastName, 'Last Name *')),
                      ]),
                      const SizedBox(height: 12),
                      _field(_workEmail, 'Work Email *', validator: (v) {
                        if (v == null || v.trim().isEmpty) return 'Required';
                        final re = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
                        if (!re.hasMatch(v.trim())) return 'Invalid email';
                        return null;
                      }),
                      const SizedBox(height: 12),
                      _field(_company, 'Company *'),
                      const SizedBox(height: 12),
                      Row(children: [
                        Expanded(child: _dropdown('Job Title *', _jobTitle, (v) => setState(() => _jobTitle = v), const ['CISO','Compliance Manager','Risk Manager','Legal Counsel','Privacy Officer','CEO/Founder','CTO','Other'])),
                        const SizedBox(width: 12),
                        Expanded(child: _dropdown('Company Size', _companySize, (v) => setState(() => _companySize = v), const ['1-10','11-50','51-250','251-1000','1000+'])),
                      ]),
                      const SizedBox(height: 12),
                      _dropdown('Primary Compliance Interest *', _interest, (v) => setState(() => _interest = v), const ['ISO 27001','GDPR','AI Governance','SOC 2','Multiple Standards','General Exploration']),
                      const SizedBox(height: 12),
                      Row(children: [
                        Expanded(child: _field(_password, 'Create Password *', obscure: true, validator: (v) {
                          if (v == null || v.length < 8) return 'Min 8 characters';
                          return null;
                        })),
                        const SizedBox(width: 12),
                        Expanded(child: _field(_confirmPassword, 'Confirm Password *', obscure: true, validator: (v) {
                          if (v != _password.text) return 'Passwords do not match';
                          return null;
                        })),
                      ]),
                      const SizedBox(height: 12),
                      _checkbox('I agree to the processing of my personal data for demo access purposes. *', _agreeProcessing, (v) => setState(() => _agreeProcessing = v ?? false), required: true),
                      _checkbox('I have read and understood the Privacy Policy *', _agreePrivacy, (v) => setState(() => _agreePrivacy = v ?? false), required: true),
                      _checkbox('I agree to receive product communications (optional)', _agreeMarketing, (v) => setState(() => _agreeMarketing = v ?? false)),
                      const SizedBox(height: 12),
                      Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                        TextButton(onPressed: _submitting ? null : () => Navigator.of(context).pop(), child: const Text('Cancel')),
                        const SizedBox(width: 8),
                        ElevatedButton(onPressed: _submitting ? null : _submit, child: Text(_submitting ? 'Submitting...' : 'Request Access')),
                      ])
                    ],
                  ),
                ),
              ],
            ),
          ),
          secondChild: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.check_circle, color: Color(0xFF10B981), size: 48),
                SizedBox(height: 12),
                Text('Access Request Submitted!', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700)),
                SizedBox(height: 8),
                Text('We\'ve sent a verification email to your inbox. Please check your email and click the verification link to access the demo platform.'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _field(TextEditingController c, String label, {bool obscure = false, String? Function(String?)? validator}) => TextFormField(
    controller: c,
    obscureText: obscure,
    decoration: InputDecoration(labelText: label),
    validator: validator ?? (v) => v == null || v.trim().isEmpty ? 'Required' : null,
  );

  Widget _dropdown(String label, String? value, ValueChanged<String?> onChanged, List<String> items) => DropdownButtonFormField<String>(
    value: value,
    decoration: InputDecoration(labelText: label),
    items: items.map((e) => DropdownMenuItem(value: e, child: Text(e))).toList(),
    onChanged: onChanged,
    validator: (v) => label.contains('*') && (v == null || v.isEmpty) ? 'Required' : null,
  );

  Widget _checkbox(String label, bool value, ValueChanged<bool?> onChanged, {bool required = false}) => CheckboxListTile(
    value: value,
    onChanged: onChanged,
    controlAffinity: ListTileControlAffinity.leading,
    title: Text(label),
    subtitle: required && !value ? const Text('Required', style: TextStyle(color: Colors.red)) : null,
  );
}


